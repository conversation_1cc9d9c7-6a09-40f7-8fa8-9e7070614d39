package v1

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
)

type BusinessCompanyCreateParams struct {
	Name      string             `json:"name" binding:"required" example:"企业名称"`
	Industry  uint               `json:"industry" example:"1"`
	Type      uint               `json:"type" example:"1"`
	Size      uint               `json:"size" example:"1"`
	Cert      pq.Int64Array      `json:"cert" example:"1,2"`
	Contact   string             `json:"contact" example:"联系人"`
	Area      string             `json:"area" example:"地区"`
	Address   string             `json:"address" example:"详细地址"`
	Phone     string             `json:"phone" example:"电话号码"`
	Email     string             `json:"email" example:"邮箱"`
	Website   string             `json:"website" example:"网站地址"`
	Logo      string             `json:"logo" example:"https://example.com/logo.jpg"`
	TianyanId string             `json:"tianyanId" example:"123"`
	Summary   string             `json:"summary" example:"企业简介"`
	Detail    string             `json:"detail" example:"企业详情"`
	Albums    []UploadFileParams `json:"albums"`
	Tags      datatypes.JSON     `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Order     int                `json:"order" example:"1"`
	Flag      pq.Int64Array      `json:"flag" example:"1,2"`
	Status    bool               `json:"status" example:"true"`
}

type BusinessCompanyUpdateParams struct {
	Name      string             `json:"name" example:"企业名称"`
	Industry  uint               `json:"industry" example:"1"`
	Type      uint               `json:"type" example:"1"`
	Size      uint               `json:"size" example:"1"`
	Cert      pq.Int64Array      `json:"cert" example:"1,2"`
	Contact   string             `json:"contact" example:"联系人"`
	Area      string             `json:"area" example:"地区"`
	Address   string             `json:"address" example:"详细地址"`
	Phone     string             `json:"phone" example:"电话号码"`
	Email     string             `json:"email" example:"邮箱"`
	Website   string             `json:"website" example:"网站地址"`
	Logo      string             `json:"logo" example:"https://example.com/logo.jpg"`
	TianyanId string             `json:"tianyanId" example:"123"`
	Summary   string             `json:"summary" example:"企业简介"`
	Detail    string             `json:"detail" example:"企业详情"`
	Albums    []UploadFileParams `json:"albums"`
	Tags      datatypes.JSON     `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Order     int                `json:"order" example:"1"`
	Flag      pq.Int64Array      `json:"flag" example:"1,2"`
	Status    bool               `json:"status" example:"true"`
}

type BusinessCompanyResponse struct {
	ID        uint               `json:"id"`
	Name      string             `json:"name"`
	Industry  uint               `json:"industry"`
	Type      uint               `json:"type"`
	Size      uint               `json:"size"`
	Cert      pq.Int64Array      `json:"cert"`
	Contact   string             `json:"contact"`
	Area      string             `json:"area"`
	Address   string             `json:"address"`
	Phone     string             `json:"phone"`
	Email     string             `json:"email"`
	Website   string             `json:"website"`
	Logo      string             `json:"logo"`
	TianyanId string             `json:"tianyanId"`
	Summary   string             `json:"summary"`
	Detail    string             `json:"detail"`
	Albums    []UploadFileParams `json:"albums"`
	Tags      datatypes.JSON     `json:"tags"`
	Order     int                `json:"order"`
	Flag      pq.Int64Array      `json:"flag"`
	Status    bool               `json:"status"`
	CreatedAt string             `json:"createdAt"`
	UpdatedAt string             `json:"updatedAt"`
}
