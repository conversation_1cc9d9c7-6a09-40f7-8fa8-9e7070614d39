package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SysLogHandler struct {
	*Handler
	sysLogService service.SysLogService
}

func NewSysLogHandler(
	handler *Handler,
	sysLogService service.SysLogService,
) *SysLogHandler {
	return &SysLogHandler{
		Handler:       handler,
		sysLogService: sysLogService,
	}
}

// Delete godoc
// @Summary 删除日志
// @Schemes
// @Description 删除指定ID的日志
// @Tags 系统模块,日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "日志ID"
// @Success 200 {object} v1.Response
// @Router /system/logs/{id} [delete]
func (h *SysLogHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysLogService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除日志
// @Schemes
// @Description 批量删除指定IDs的日志
// @Tags 系统模块,日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "日志IDs"
// @Success 200 {object} v1.Response
// @Router /system/logs [delete]
func (h *SysLogHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysLogService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取日志
// @Schemes
// @Description 获取指定ID的日志信息
// @Tags 系统模块,日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "日志ID"
// @Success 200 {object} v1.Response{data=v1.SysLogResponse}
// @Router /system/logs/{id} [get]
func (h *SysLogHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	log, err := h.sysLogService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, log)
}

// List godoc
// @Summary 获取日志列表
// @Schemes
// @Description 分页获取日志列表
// @Tags 系统模块,日志管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param method query string false "请求方法筛选" example:"GET"
// @Param code query int false "状态码筛选" example:"200"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysLogResponse}}
// @Router /system/logs [get]
func (h *SysLogHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.sysLogService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
