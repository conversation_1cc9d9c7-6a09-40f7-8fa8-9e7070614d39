package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

type BusinessAppService interface {
	Create(ctx context.Context, req *v1.BusinessAppCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessAppUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessAppResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessAppService(
	service *Service,
	businessAppRepository repository.BusinessAppRepository,
) BusinessAppService {
	return &businessAppService{
		Service:               service,
		businessAppRepository: businessAppRepository,
	}
}

type businessAppService struct {
	*Service
	businessAppRepository repository.BusinessAppRepository
}

// 应用相关方法实现
func (s *businessAppService) Create(ctx context.Context, req *v1.BusinessAppCreateParams) error {
	app := &model.BusinessApp{}
	if err := copier.Copy(app, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessAppRepository.Create(ctx, app); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessAppService) Update(ctx context.Context, id uint, req *v1.BusinessAppUpdateParams) error {
	app, err := s.businessAppRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(app, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessAppRepository.Update(ctx, app); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessAppService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessAppRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessAppService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessAppRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessAppService) Get(ctx context.Context, id uint) (*v1.BusinessAppResponse, error) {
	app, err := s.businessAppRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.BusinessAppResponse{}
	if err := copier.Copy(response, app); err != nil {
		return nil, err
	}

	response.CreatedAt = app.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = app.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *businessAppService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 别名筛选
	slug := ctx.(*gin.Context).DefaultQuery("slug", "")
	if slug != "" {
		params.AddFilter("slug_like", slug)
	}

	apps, total, err := s.businessAppRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessAppResponse, 0, len(apps))
	for _, app := range apps {
		response := &v1.BusinessAppResponse{}
		if err := copier.Copy(response, app); err != nil {
			return nil, err
		}

		response.CreatedAt = app.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = app.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
