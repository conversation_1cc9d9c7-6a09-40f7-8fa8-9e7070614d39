package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type BusinessTalentRepository interface {
	Create(ctx context.Context, talent *model.BusinessTalent) error
	Update(ctx context.Context, talent *model.BusinessTalent) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.BusinessTalent, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.BusinessTalent, int64, error)
}

func NewBusinessTalentRepository(
	repository *Repository,
) BusinessTalentRepository {
	return &businessTalentRepository{
		Repository: repository,
	}
}

type businessTalentRepository struct {
	*Repository
}

func (r *businessTalentRepository) Create(ctx context.Context, talent *model.BusinessTalent) error {
	if err := r.DB(ctx).Create(talent).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessTalentRepository) Update(ctx context.Context, talent *model.BusinessTalent) error {
	if err := r.DB(ctx).Save(talent).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessTalentRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessTalent{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessTalentRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessTalent{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessTalentRepository) Get(ctx context.Context, id uint) (*model.BusinessTalent, error) {
	var talent model.BusinessTalent
	if err := r.DB(ctx).First(&talent, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &talent, nil
}

func (r *businessTalentRepository) List(ctx context.Context, params *pagination.Params) ([]*model.BusinessTalent, int64, error) {
	var records []*model.BusinessTalent
	var total int64

	db := r.DB(ctx).Model(&model.BusinessTalent{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.BusinessTalent{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
