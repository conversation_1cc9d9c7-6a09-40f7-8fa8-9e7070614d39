# Nunu — A CLI tool for building go aplication.


Nunu是一个基于Golang的应用脚手架，它的名字来自于英雄联盟中的游戏角色，一个骑在雪怪肩膀上的小男孩。和努努一样，该项目也是站在巨人的肩膀上，它是由Golang生态中各种非常流行的库整合而成的，它们的组合可以帮助你快速构建一个高效、可靠的应用程序。

[英文介绍](https://github.com/go-nunu/nunu/blob/main/README.md)

![Nunu](https://github.com/go-nunu/nunu/blob/main/.github/assets/banner.png)

## 文档
* [使用指南](https://github.com/go-nunu/nunu/blob/main/docs/zh/guide.md)
* [分层架构](https://github.com/go-nunu/nunu/blob/main/docs/zh/architecture.md)
* [上手教程](https://github.com/go-nunu/nunu/blob/main/docs/zh/tutorial.md)
* [高效编写单元测试](https://github.com/go-nunu/nunu/blob/main/docs/zh/unit_testing.md)

## 许可证

Nunu是根据MIT许可证发布的。有关更多信息，请参见[LICENSE](LICENSE)文件。

## MinIO Access Policy
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          "*"
        ]
      },
      "Action": [
        "s3:GetObject"
      ],
      "Resource": [
        "arn:aws:s3:::logistics/*"
      ]
    }
  ]
}
```

## pm2部署项目
```
pm2 start pm2.json
```

### 常用操作命令
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs qindong-server

# 重启应用
pm2 restart qindong-server

# 停止应用
pm2 stop qindong-server

# 删除应用
pm2 delete qindong-server

# 重新加载配置
pm2 reload qindong-server

# 监控应用
pm2 monit
```

### 部署前准备
```bash
# 1. 构建应用
make build

# 2. 确保配置文件存在
ls config/prod.yml

# 3. 启动依赖服务
cd deploy/docker-compose && docker compose up -d
```

### 生产环境建议
- 使用 `pm2 startup` 设置开机自启
- 定期备份 PM2 配置: `pm2 save`