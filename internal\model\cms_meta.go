package model

import "gorm.io/gorm"

type CmsMeta struct {
	gorm.Model
	Name     string `gorm:"size:64; not null; unique; comment:名称"`
	Slug     string `gorm:"size:64; not null; unique; comment:别名"`
	Cover    string `gorm:"size:255; comment:封面"`
	Summary  string `gorm:"type:text; comment:描述"`
	Order    int    `gorm:"default:0; index; comment:排序"`
	Status   bool   `gorm:"default:false; index; comment:状态"`
	ParentId uint   `gorm:"default:0; index; comment:父级ID"`
}

func (m *CmsMeta) TableName() string {
	return "cms_meta"
}
