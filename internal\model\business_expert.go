package model

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type BusinessExpert struct {
	gorm.Model
	Name   string         `gorm:"size:255; not null; index; comment:专家名称"`
	Gender uint           `gorm:"default:0; index; comment:性别"`
	Age    uint           `gorm:"default:0; index; comment:年龄"`
	Edu    uint           `gorm:"default:0; index; comment:学历"`
	Exp    uint           `gorm:"default:0; index; comment:经验"`
	Type   pq.Int64Array  `gorm:"type:integer[]; comment:擅长领域"`
	Skills datatypes.JSON `gorm:"type:jsonb; comment:技能"`
	Area   string         `gorm:"size:32; comment:地区"`
	Phone  string         `gorm:"size:20; comment:电话"`
	Email  string         `gorm:"size:64; comment:邮箱"`
	Avatar string         `gorm:"size:255; comment:头像"`
	Tags    datatypes.JSON `gorm:"type:jsonb; comment:标签"`
	Summary string         `gorm:"type:text; comment:专家简介"`
	Detail  string         `gorm:"type:text; comment:详情"`
	Party  bool           `gorm:"default:false; index; comment:是否参与"`
	Order  int            `gorm:"default:0; index; comment:排序"`
	Flag   pq.Int64Array  `gorm:"type:integer[]; comment:标志"`
	Status bool           `gorm:"default:false; index; comment:状态"`
}

func (m *BusinessExpert) TableName() string {
	return "business_expert"
}
