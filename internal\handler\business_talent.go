package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BusinessTalentHandler struct {
	*Handler
	businessTalentService service.BusinessTalentService
}

func NewBusinessTalentHandler(
	handler *Handler,
	businessTalentService service.BusinessTalentService,
) *BusinessTalentHandler {
	return &BusinessTalentHandler{
		Handler:               handler,
		businessTalentService: businessTalentService,
	}
}

// Create godoc
// @Summary 创建人才
// @Schemes
// @Description 创建新的人才记录
// @Tags 业务模块,人才管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BusinessTalentCreateParams true "人才信息"
// @Success 200 {object} v1.Response
// @Router /business/talents [post]
func (h *BusinessTalentHandler) Create(ctx *gin.Context) {
	var req v1.BusinessTalentCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessTalentService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新人才
// @Schemes
// @Description 更新指定ID的人才信息
// @Tags 业务模块,人才管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "人才ID"
// @Param request body v1.BusinessTalentUpdateParams true "人才信息"
// @Success 200 {object} v1.Response
// @Router /business/talents/{id} [patch]
func (h *BusinessTalentHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.BusinessTalentUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessTalentService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除人才
// @Schemes
// @Description 删除指定ID的人才
// @Tags 业务模块,人才管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "人才ID"
// @Success 200 {object} v1.Response
// @Router /business/talents/{id} [delete]
func (h *BusinessTalentHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessTalentService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除人才
// @Schemes
// @Description 批量删除指定IDs的人才
// @Tags 业务模块,人才管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "人才IDs"
// @Success 200 {object} v1.Response
// @Router /business/talents [delete]
func (h *BusinessTalentHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessTalentService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取人才
// @Schemes
// @Description 获取指定ID的人才信息
// @Tags 业务模块,人才管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "人才ID"
// @Success 200 {object} v1.Response{data=v1.BusinessTalentResponse}
// @Router /business/talents/{id} [get]
func (h *BusinessTalentHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	talent, err := h.businessTalentService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, talent)
}

// List godoc
// @Summary 获取人才列表
// @Schemes
// @Description 分页获取人才列表
// @Tags 业务模块,人才管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param name query string false "姓名筛选" example:"张三"
// @Param gender query int false "性别筛选" example:"1"
// @Param area query string false "地区筛选" example:"上海"
// @Param party query bool false "是否参与筛选" example:"true"
// @Param status query bool false "状态筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.BusinessTalentResponse}}
// @Router /business/talents [get]
func (h *BusinessTalentHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.businessTalentService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
