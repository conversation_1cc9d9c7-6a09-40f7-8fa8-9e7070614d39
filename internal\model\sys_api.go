package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type SysApi struct {
	gorm.Model
	Path    string         `gorm:"size:255; not null; comment:路径"`
	Method  string         `gorm:"size:255; not null; comment:方法"`
	Tags    datatypes.JSON `gorm:"type:jsonb; comment:标签"`
	Summary string         `gorm:"type:text; comment:描述"`
	Status  bool           `gorm:"default:false; index; comment:状态"`
}

func (m *SysApi) TableName() string {
	return "sys_api"
}
