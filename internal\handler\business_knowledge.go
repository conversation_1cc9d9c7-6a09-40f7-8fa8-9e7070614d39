package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BusinessKnowledgeHandler struct {
	*Handler
	businessKnowledgeService service.BusinessKnowledgeService
}

func NewBusinessKnowledgeHandler(
	handler *Handler,
	businessKnowledgeService service.BusinessKnowledgeService,
) *BusinessKnowledgeHandler {
	return &BusinessKnowledgeHandler{
		Handler:                  handler,
		businessKnowledgeService: businessKnowledgeService,
	}
}

// Create godoc
// @Summary 创建知识
// @Schemes
// @Description 创建新的知识记录
// @Tags 业务模块,知识管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BusinessKnowledgeCreateParams true "知识信息"
// @Success 200 {object} v1.Response
// @Router /business/knowledges [post]
func (h *BusinessKnowledgeHandler) Create(ctx *gin.Context) {
	var req v1.BusinessKnowledgeCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessKnowledgeService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新知识
// @Schemes
// @Description 更新指定ID的知识信息
// @Tags 业务模块,知识管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "知识ID"
// @Param request body v1.BusinessKnowledgeUpdateParams true "知识信息"
// @Success 200 {object} v1.Response
// @Router /business/knowledges/{id} [patch]
func (h *BusinessKnowledgeHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.BusinessKnowledgeUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessKnowledgeService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除知识
// @Schemes
// @Description 删除指定ID的知识
// @Tags 业务模块,知识管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "知识ID"
// @Success 200 {object} v1.Response
// @Router /business/knowledges/{id} [delete]
func (h *BusinessKnowledgeHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessKnowledgeService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除知识
// @Schemes
// @Description 批量删除指定IDs的知识
// @Tags 业务模块,知识管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "知识IDs"
// @Success 200 {object} v1.Response
// @Router /business/knowledges [delete]
func (h *BusinessKnowledgeHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessKnowledgeService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取知识
// @Schemes
// @Description 获取指定ID的知识信息
// @Tags 业务模块,知识管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "知识ID"
// @Success 200 {object} v1.Response{data=v1.BusinessKnowledgeResponse}
// @Router /business/knowledges/{id} [get]
func (h *BusinessKnowledgeHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	knowledge, err := h.businessKnowledgeService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, knowledge)
}

// List godoc
// @Summary 获取知识列表
// @Schemes
// @Description 分页获取知识列表
// @Tags 业务模块,知识管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param industry query int false "行业筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.BusinessKnowledgeResponse}}
// @Router /business/knowledges [get]
func (h *BusinessKnowledgeHandler) List(ctx *gin.Context) {
	params := &pagination.Params{}
	if err := ctx.ShouldBindQuery(params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.businessKnowledgeService.List(ctx, params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
