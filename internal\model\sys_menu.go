package model

import "gorm.io/gorm"

type SysMenu struct {
	gorm.Model
	MenuType   int    `gorm:"default:0; comment:菜单类型"`
	MenuName   string `gorm:"size:255; comment:菜单名称"`
	RouteName  string `gorm:"size:255; comment:路由名称"`
	RoutePath  string `gorm:"size:255; comment:路由路径"`
	Layout     string `gorm:"size:255; comment:布局"`
	Component  string `gorm:"size:255; comment:组件"`
	Icon       string `gorm:"size:255; comment:图标"`
	Order      int    `gorm:"default:0; index; comment:排序"`
	Href       string `gorm:"size:255; comment:链接"`
	Constant   bool   `gorm:"default:false; comment:常量"`
	HideInMenu bool   `gorm:"default:false; comment:隐藏菜单"`
	KeepAlive  bool   `gorm:"default:false; comment:保持活动"`
	MultiTab   bool   `gorm:"default:false; comment:多标签"`
	Status     bool   `gorm:"default:false; index; comment:状态"`
	ParentId   uint   `gorm:"default:0; index; comment:父级ID"`
}

func (m *SysMenu) TableName() string {
	return "sys_menu"
}
