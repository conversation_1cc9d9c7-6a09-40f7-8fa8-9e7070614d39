package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

type SysDeptService interface {
	Create(ctx context.Context, req *v1.SysDeptCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysDeptUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysDeptResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysDeptService(
	service *Service,
	sysDeptRepository repository.SysDeptRepository,
) SysDeptService {
	return &sysDeptService{
		Service:           service,
		sysDeptRepository: sysDeptRepository,
	}
}

type sysDeptService struct {
	*Service
	sysDeptRepository repository.SysDeptRepository
}

// 部门相关方法实现
func (s *sysDeptService) Create(ctx context.Context, req *v1.SysDeptCreateParams) error {
	dept := &model.SysDept{}
	if err := copier.Copy(dept, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDeptRepository.Create(ctx, dept); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDeptService) Update(ctx context.Context, id uint, req *v1.SysDeptUpdateParams) error {
	dept, err := s.sysDeptRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(dept, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDeptRepository.Update(ctx, dept); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDeptService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDeptRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDeptService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysDeptRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysDeptService) Get(ctx context.Context, id uint) (*v1.SysDeptResponse, error) {
	dept, err := s.sysDeptRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.SysDeptResponse{}
	if err := copier.Copy(response, dept); err != nil {
		return nil, err
	}

	response.CreatedAt = dept.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = dept.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *sysDeptService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 编码筛选
	code := ctx.(*gin.Context).DefaultQuery("code", "")
	if code != "" {
		params.AddFilter("code", code)
	}

	depts, total, err := s.sysDeptRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysDeptResponse, 0, len(depts))
	for _, dept := range depts {
		response := &v1.SysDeptResponse{}
		if err := copier.Copy(response, dept); err != nil {
			return nil, err
		}

		response.CreatedAt = dept.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = dept.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
