package v1

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
)

type BusinessJobCreateParams struct {
	CompanyId  uint           `json:"companyId" example:"1"`
	Title      string         `json:"title" binding:"required" example:"职位名称"`
	Type       uint           `json:"type" example:"1"`
	SalaryType uint           `json:"salaryType" example:"1"`
	Salary     datatypes.JSON `json:"salary" swaggertype:"array,string" example:"5000,10000"`
	Tags       datatypes.JSON `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Detail     string         `json:"detail" example:"职位详情"`
	Area       string         `json:"area" example:"北京"`
	Contact    string         `json:"contact" example:"张三"`
	Phone      string         `json:"phone" example:"***********"`
	Email      string         `json:"email" example:"<EMAIL>"`
	Order      int            `json:"order" example:"1"`
	Flag       pq.Int64Array  `json:"flag" example:"1,2"`
	Status     bool           `json:"status" example:"true"`
}

type BusinessJobUpdateParams struct {
	CompanyId  uint           `json:"companyId" example:"1"`
	Title      string         `json:"title" example:"职位名称"`
	Type       uint           `json:"type" example:"1"`
	SalaryType uint           `json:"salaryType" example:"1"`
	Salary     datatypes.JSON `json:"salary" swaggertype:"array,string" example:"5000,10000"`
	Tags       datatypes.JSON `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Detail     string         `json:"detail" example:"职位详情"`
	Area       string         `json:"area" example:"北京"`
	Contact    string         `json:"contact" example:"张三"`
	Phone      string         `json:"phone" example:"***********"`
	Email      string         `json:"email" example:"<EMAIL>"`
	Order      int            `json:"order" example:"1"`
	Flag       pq.Int64Array  `json:"flag" example:"1,2"`
	Status     bool           `json:"status" example:"true"`
}

type BusinessJobResponse struct {
	ID         uint                     `json:"id"`
	CompanyId  uint                     `json:"companyId"`
	Title      string                   `json:"title"`
	Type       uint                     `json:"type"`
	SalaryType uint                     `json:"salaryType"`
	Salary     datatypes.JSON           `json:"salary"`
	Tags       datatypes.JSON           `json:"tags"`
	Detail     string                   `json:"detail"`
	Area       string                   `json:"area"`
	Contact    string                   `json:"contact"`
	Phone      string                   `json:"phone"`
	Email      string                   `json:"email"`
	Order      int                      `json:"order"`
	Flag       pq.Int64Array            `json:"flag"`
	Status     bool                     `json:"status"`
	CreatedAt  string                   `json:"createdAt"`
	UpdatedAt  string                   `json:"updatedAt"`
	Company    *BusinessCompanyResponse `json:"company,omitempty"` // 企业信息
}
