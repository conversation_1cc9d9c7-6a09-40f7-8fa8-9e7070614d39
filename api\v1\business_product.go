package v1

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
)

type BusinessProductCreateParams struct {
	CompanyId uint               `json:"companyId" binding:"required" example:"1"`
	Name      string             `json:"name" binding:"required" example:"产品名称"`
	Code      string             `json:"code" binding:"required" example:"PRD-001"`
	Type      uint               `json:"type" example:"1"`
	Spec      string             `json:"spec" example:"规格信息"`
	Material  string             `json:"material" example:"材料信息"`
	Standard  string             `json:"standard" example:"标准信息"`
	Surface   string             `json:"surface" example:"表面处理"`
	Strength  string             `json:"strength" example:"强度信息"`
	Cover     string             `json:"cover" example:"https://example.com/cover.jpg"`
	Detail    string             `json:"detail" example:"产品详细描述"`
	Albums    []UploadFileParams `json:"albums"`
	Tags      datatypes.JSON     `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Order     int                `json:"order" example:"1"`
	Flag      pq.Int64Array      `json:"flag" example:"1,2"`
	Status    bool               `json:"status" example:"true"`
}

type BusinessProductUpdateParams struct {
	CompanyId uint               `json:"companyId" example:"1"`
	Name      string             `json:"name" example:"产品名称"`
	Code      string             `json:"code" example:"PRD-001"`
	Type      uint               `json:"type" example:"1"`
	Spec      string             `json:"spec" example:"规格信息"`
	Material  string             `json:"material" example:"材料信息"`
	Standard  string             `json:"standard" example:"标准信息"`
	Surface   string             `json:"surface" example:"表面处理"`
	Strength  string             `json:"strength" example:"强度信息"`
	Cover     string             `json:"cover" example:"https://example.com/cover.jpg"`
	Detail    string             `json:"detail" example:"产品详细描述"`
	Albums    []UploadFileParams `json:"albums"`
	Tags      datatypes.JSON     `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Order     int                `json:"order" example:"1"`
	Flag      pq.Int64Array      `json:"flag" example:"1,2"`
	Status    bool               `json:"status" example:"true"`
}

type BusinessProductResponse struct {
	ID        uint                     `json:"id"`
	CompanyId uint                     `json:"companyId"`
	Name      string                   `json:"name"`
	Code      string                   `json:"code"`
	Type      uint                     `json:"type"`
	Spec      string                   `json:"spec"`
	Material  string                   `json:"material"`
	Standard  string                   `json:"standard"`
	Surface   string                   `json:"surface"`
	Strength  string                   `json:"strength"`
	Cover     string                   `json:"cover"`
	Detail    string                   `json:"detail"`
	Albums    []UploadFileParams       `json:"albums"`
	Tags      datatypes.JSON           `json:"tags"`
	Order     int                      `json:"order"`
	Flag      pq.Int64Array            `json:"flag"`
	Status    bool                     `json:"status"`
	CreatedAt string                   `json:"createdAt"`
	UpdatedAt string                   `json:"updatedAt"`
	Company   *BusinessCompanyResponse `json:"company,omitempty"` // 企业信息
}
