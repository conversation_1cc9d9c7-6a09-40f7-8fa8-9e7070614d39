package model

import (
	v1 "daisy-server/api/v1"

	"github.com/lib/pq"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type BusinessProduct struct {
	gorm.Model
	CompanyId uint                  `gorm:"default:0; index; comment:公司ID"`
	Name      string                `gorm:"size:255; not null; index; comment:产品名称"`
	Code      string                `gorm:"size:255; not null; index; comment:产品编码"`
	Type      uint                  `gorm:"default:0; index; comment:产品类型"`
	Spec      string                `gorm:"size:255; not null; index; comment:产品规格"`
	Material  string                `gorm:"size:255; not null; index; comment:产品材料"`
	Standard  string                `gorm:"size:255; not null; index; comment:产品标准"`
	Surface   string                `gorm:"size:255; not null; index; comment:产品表面"`
	Strength  string                `gorm:"size:255; not null; index; comment:产品强度"`
	Cover     string                `gorm:"size:255; comment:产品封面"`
	Detail    string                `gorm:"type:text; comment:产品详情"`
	Albums    []v1.UploadFileParams `gorm:"type:jsonb; serializer:json; comment:产品相册"`
	Tags      datatypes.JSON        `gorm:"type:jsonb; comment:产品标签"`
	Order     int                   `gorm:"default:0; index; comment:排序"`
	Flag      pq.Int64Array         `gorm:"type:integer[]; comment:标志"`
	Status    bool                  `gorm:"default:false; index; comment:状态"`
}

func (m *BusinessProduct) TableName() string {
	return "business_product"
}
