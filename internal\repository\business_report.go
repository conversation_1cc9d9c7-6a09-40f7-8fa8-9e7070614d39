package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type BusinessReportRepository interface {
	Create(ctx context.Context, report *model.BusinessReport) error
	Update(ctx context.Context, report *model.BusinessReport) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.BusinessReport, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.BusinessReport, int64, error)
}

func NewBusinessReportRepository(
	repository *Repository,
) BusinessReportRepository {
	return &businessReportRepository{
		Repository: repository,
	}
}

type businessReportRepository struct {
	*Repository
}

func (r *businessReportRepository) Create(ctx context.Context, report *model.BusinessReport) error {
	if err := r.DB(ctx).Create(report).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessReportRepository) Update(ctx context.Context, report *model.BusinessReport) error {
	if err := r.DB(ctx).Save(report).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessReportRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessReport{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessReportRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessReport{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessReportRepository) Get(ctx context.Context, id uint) (*model.BusinessReport, error) {
	var report model.BusinessReport
	if err := r.DB(ctx).First(&report, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &report, nil
}

func (r *businessReportRepository) List(ctx context.Context, params *pagination.Params) ([]*model.BusinessReport, int64, error) {
	var records []*model.BusinessReport
	var total int64

	db := r.DB(ctx).Model(&model.BusinessReport{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.BusinessReport{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
