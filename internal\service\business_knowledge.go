package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

type BusinessKnowledgeService interface {
	Create(ctx context.Context, req *v1.BusinessKnowledgeCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessKnowledgeUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessKnowledgeResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessKnowledgeService(
	service *Service,
	businessKnowledgeRepository repository.BusinessKnowledgeRepository,
) BusinessKnowledgeService {
	return &businessKnowledgeService{
		Service:                     service,
		businessKnowledgeRepository: businessKnowledgeRepository,
	}
}

type businessKnowledgeService struct {
	*Service
	businessKnowledgeRepository repository.BusinessKnowledgeRepository
}

// 知识管理相关方法实现
func (s *businessKnowledgeService) Create(ctx context.Context, req *v1.BusinessKnowledgeCreateParams) error {
	knowledge := &model.BusinessKnowledge{}
	if err := copier.Copy(knowledge, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessKnowledgeRepository.Create(ctx, knowledge); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessKnowledgeService) Update(ctx context.Context, id uint, req *v1.BusinessKnowledgeUpdateParams) error {
	knowledge, err := s.businessKnowledgeRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(knowledge, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessKnowledgeRepository.Update(ctx, knowledge); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessKnowledgeService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessKnowledgeRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessKnowledgeService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessKnowledgeRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessKnowledgeService) Get(ctx context.Context, id uint) (*v1.BusinessKnowledgeResponse, error) {
	knowledge, err := s.businessKnowledgeRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.BusinessKnowledgeResponse{}
	if err := copier.Copy(response, knowledge); err != nil {
		return nil, err
	}

	response.CreatedAt = knowledge.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = knowledge.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *businessKnowledgeService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 标题筛选
	title := ctx.(*gin.Context).DefaultQuery("title", "")
	if title != "" {
		params.AddFilter("title_like", title)
	}

	// 行业筛选
	industry := ctx.(*gin.Context).DefaultQuery("industry", "")
	if industry != "" {
		params.AddFilter("industry", industry)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	knowledges, total, err := s.businessKnowledgeRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessKnowledgeResponse, 0, len(knowledges))
	for _, knowledge := range knowledges {
		response := &v1.BusinessKnowledgeResponse{}
		if err := copier.Copy(response, knowledge); err != nil {
			return nil, err
		}

		response.CreatedAt = knowledge.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = knowledge.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
