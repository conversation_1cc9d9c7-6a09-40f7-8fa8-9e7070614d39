package v1

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
)

type BusinessKnowledgeCreateParams struct {
	Title    string             `json:"title" binding:"required" example:"知识标题"`
	Industry uint               `json:"industry" example:"1"`
	Summary  string             `json:"summary" example:"知识摘要"`
	Content  string             `json:"content" example:"知识内容"`
	Tags     datatypes.JSON     `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Cover    string             `json:"cover" example:"https://example.com/cover.jpg"`
	Files    []UploadFileParams `json:"files"`
	Order    int                `json:"order" example:"1"`
	Flag     pq.Int64Array      `json:"flag" example:"1,2"`
	Status   bool               `json:"status" example:"true"`
}

type BusinessKnowledgeUpdateParams struct {
	Title    string             `json:"title" example:"知识标题"`
	Industry uint               `json:"industry" example:"1"`
	Summary  string             `json:"summary" example:"知识摘要"`
	Content  string             `json:"content" example:"知识内容"`
	Tags     datatypes.JSON     `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Cover    string             `json:"cover" example:"https://example.com/cover.jpg"`
	Files    []UploadFileParams `json:"files"`
	Order    int                `json:"order" example:"1"`
	Flag     pq.Int64Array      `json:"flag" example:"1,2"`
	Status   bool               `json:"status" example:"true"`
}

type BusinessKnowledgeResponse struct {
	ID        uint               `json:"id"`
	Title     string             `json:"title"`
	Industry  uint               `json:"industry"`
	Summary   string             `json:"summary"`
	Content   string             `json:"content"`
	Tags      datatypes.JSON     `json:"tags"`
	Cover     string             `json:"cover"`
	Files     []UploadFileParams `json:"files"`
	Order     int                `json:"order"`
	Flag      pq.Int64Array      `json:"flag"`
	Status    bool               `json:"status"`
	CreatedAt string             `json:"createdAt"`
	UpdatedAt string             `json:"updatedAt"`
}
