package model

import (
	"github.com/lib/pq"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type BusinessJob struct {
	gorm.Model
	CompanyId  uint           `gorm:"default:0; index; comment:公司ID"`
	Title      string         `gorm:"size:255; not null; index; comment:职位名称"`
	Type  	   uint           `gorm:"default:0; index; comment:岗位类型"`
	SalaryType uint           `gorm:"default:0; index; comment:薪资类型"`
	Salary     datatypes.JSON `gorm:"type:jsonb; comment:薪资"`
	Tags       datatypes.JSON `gorm:"type:jsonb; comment:标签"`
	Detail     string         `gorm:"type:text; comment:详情"`
	Area       string         `gorm:"size:32; comment:地区"`
	Contact    string         `gorm:"size:64; comment:联系人"`
	Phone      string         `gorm:"size:20; comment:电话"`
	Email      string         `gorm:"size:64; comment:邮箱"`
	Order      int            `gorm:"default:0; index; comment:排序"`
	Flag       pq.Int64Array  `gorm:"type:integer[]; comment:标志"`
	Status     bool           `gorm:"default:false; index; comment:状态"`
}

func (m *BusinessJob) TableName() string {
	return "business_job"
}
