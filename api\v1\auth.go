package v1

// AuthLoginParams 认证请求
type AuthLoginParams struct {
	Username string `json:"username" binding:"required" example:"admin"`
	Password string `json:"password" binding:"required" example:"123456"`
}

// AuthLoginData 认证响应数据
type AuthLoginData struct {
	Token        string `json:"token"`
	RefreshToken string `json:"refreshToken"`
}

// AuthLoginResponse 认证响应
type AuthLoginResponse struct {
	Response
	Data AuthLoginData
}

// AuthRefreshParams 刷新认证请求
type AuthRefreshParams struct {
	RefreshToken string `json:"refreshToken" binding:"required" example:"refreshToken"`
}

// AuthPasswordUpdateParams 修改密码请求
type AuthPasswordUpdateParams struct {
	OldPass  string `json:"oldpass" binding:"required" example:"123456"`
	Password string `json:"password" binding:"required" example:"654321"`
	Repass   string `json:"repass" binding:"required" example:"654321"`
}

// AuthUserinfoUpdateParams 修改个人资料请求
type AuthUserinfoUpdateParams struct {
	Nickname string `json:"nickname" example:"alan"`
	Avatar   string `json:"avatar" example:"https://example.com/avatar.png"`
	Gender   uint   `json:"gender" example:"1"`
	Email    string `json:"email" binding:"required" example:"<EMAIL>"`
	Phone    string `json:"phone" example:"13800138000"`
}

// AuthUserinfoResponse 用户个人资料响应
type AuthUserinfoResponse struct {
	SysUserResponse
	Roles []*SysRoleResponse `json:"roles,omitempty"` // 角色列表
	Dept  *SysDeptResponse   `json:"dept,omitempty"`  // 部门信息
}

// AuthExistRouteParams 检查路由是否存在请求参数
type AuthExistRouteParams struct {
	RouteName string `form:"routeName" binding:"required" example:"user"`
}
