package model

import (
	v1 "daisy-server/api/v1"

	"github.com/lib/pq"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type BusinessKnowledge struct {
	gorm.Model
	Title    string                `gorm:"size:255; not null; index; comment:标题"`
	Industry uint                  `gorm:"default:0; index; comment:行业"`
	Summary  string                `gorm:"size:255; comment:摘要"`
	Content  string                `gorm:"type:text; comment:内容"`
	Tags     datatypes.JSON        `gorm:"type:jsonb; comment:标签"`
	Cover    string                `gorm:"size:255; comment:封面"`
	Files    []v1.UploadFileParams `gorm:"type:jsonb; serializer:json; comment:附件"`
	Order    int                   `gorm:"default:0; index; comment:排序"`
	Flag     pq.Int64Array         `gorm:"type:integer[]; comment:标志"`
	Status   bool                  `gorm:"default:false; index; comment:状态"`
}

func (m *BusinessKnowledge) TableName() string {
	return "business_knowledge"
}
