package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type BusinessCompanyRepository interface {
	Create(ctx context.Context, company *model.BusinessCompany) error
	Update(ctx context.Context, company *model.BusinessCompany) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.BusinessCompany, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.BusinessCompany, int64, error)
}

func NewBusinessCompanyRepository(
	repository *Repository,
) BusinessCompanyRepository {
	return &businessCompanyRepository{
		Repository: repository,
	}
}

type businessCompanyRepository struct {
	*Repository
}

func (r *businessCompanyRepository) Create(ctx context.Context, company *model.BusinessCompany) error {
	if err := r.DB(ctx).Create(company).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessCompanyRepository) Update(ctx context.Context, company *model.BusinessCompany) error {
	if err := r.DB(ctx).Save(company).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessCompanyRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessCompany{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessCompanyRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessCompany{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessCompanyRepository) Get(ctx context.Context, id uint) (*model.BusinessCompany, error) {
	var company model.BusinessCompany
	if err := r.DB(ctx).First(&company, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &company, nil
}

func (r *businessCompanyRepository) List(ctx context.Context, params *pagination.Params) ([]*model.BusinessCompany, int64, error) {
	var records []*model.BusinessCompany
	var total int64

	db := r.DB(ctx).Model(&model.BusinessCompany{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.BusinessCompany{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
