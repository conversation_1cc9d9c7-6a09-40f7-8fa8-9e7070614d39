package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

type BusinessCompanyService interface {
	Create(ctx context.Context, req *v1.BusinessCompanyCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessCompanyUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessCompanyResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessCompanyService(
	service *Service,
	businessCompanyRepository repository.BusinessCompanyRepository,
) BusinessCompanyService {
	return &businessCompanyService{
		Service:                   service,
		businessCompanyRepository: businessCompanyRepository,
	}
}

type businessCompanyService struct {
	*Service
	businessCompanyRepository repository.BusinessCompanyRepository
}

// 企业相关方法实现
func (s *businessCompanyService) Create(ctx context.Context, req *v1.BusinessCompanyCreateParams) error {
	company := &model.BusinessCompany{}
	if err := copier.Copy(company, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessCompanyRepository.Create(ctx, company); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessCompanyService) Update(ctx context.Context, id uint, req *v1.BusinessCompanyUpdateParams) error {
	company, err := s.businessCompanyRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(company, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessCompanyRepository.Update(ctx, company); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessCompanyService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessCompanyRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessCompanyService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessCompanyRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessCompanyService) Get(ctx context.Context, id uint) (*v1.BusinessCompanyResponse, error) {
	company, err := s.businessCompanyRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.BusinessCompanyResponse{}
	if err := copier.Copy(response, company); err != nil {
		return nil, err
	}

	// 手动设置时间格式化字段
	response.CreatedAt = company.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = company.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *businessCompanyService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 行业筛选
	industry := ctx.(*gin.Context).DefaultQuery("industry", "")
	if industry != "" {
		params.AddFilter("industry", industry)
	}

	// 类型筛选
	companyType := ctx.(*gin.Context).DefaultQuery("type", "")
	if companyType != "" {
		params.AddFilter("type", companyType)
	}

	// 规模筛选
	size := ctx.(*gin.Context).DefaultQuery("size", "")
	if size != "" {
		params.AddFilter("size", size)
	}

	// 资质筛选
	cert := ctx.(*gin.Context).DefaultQuery("cert", "")
	if cert != "" {
		params.AddFilter("cert_any", cert)
	}

	// 天眼查ID筛选
	tianyanId := ctx.(*gin.Context).DefaultQuery("tianyanId", "")
	if tianyanId != "" {
		params.AddFilter("tianyan_id", tianyanId)
	}

	// 地区筛选
	area := ctx.(*gin.Context).DefaultQuery("area", "")
	if area != "" {
		params.AddFilter("area_like", area)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	companies, total, err := s.businessCompanyRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessCompanyResponse, 0, len(companies))
	for _, company := range companies {
		response := &v1.BusinessCompanyResponse{}
		if err := copier.Copy(response, company); err != nil {
			return nil, err
		}

		// 手动设置时间格式化字段
		response.CreatedAt = company.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = company.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
