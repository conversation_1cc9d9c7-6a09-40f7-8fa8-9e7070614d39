{"swagger": "2.0", "info": {"description": "This is a sample server celler server.", "title": "Nunu Example API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0.0"}, "host": "localhost:8000", "paths": {"/auth/constant_routes": {"get": {"description": "获取系统常量路由", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "获取常量路由", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/exist_route": {"get": {"description": "检查指定路由名称是否存在", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "检查路由是否存在", "parameters": [{"type": "string", "description": "路由名称", "name": "routeName", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/login": {"post": {"description": "使用用户名密码登录系统", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "登录", "parameters": [{"description": "登录参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthLoginParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthLoginResponse"}}}}}, "/auth/logout": {"post": {"security": [{"Bearer": []}], "description": "注销登录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "注销", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/password": {"post": {"security": [{"Bearer": []}], "description": "修改当前登录用户的密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "修改用户密码", "parameters": [{"description": "密码修改参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthPasswordUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/refresh": {"post": {"description": "使用 refreshToken 刷新认证令牌", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "刷新令牌", "parameters": [{"description": "刷新参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthRefreshParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthLoginResponse"}}}}}, "/auth/user_routes": {"get": {"security": [{"Bearer": []}], "description": "获取当前登录用户的菜单路由", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "获取用户路由", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/auth/userinfo": {"get": {"security": [{"Bearer": []}], "description": "获取当前登录用户的用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "获取用户信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthUserinfoResponse"}}}}, "post": {"security": [{"Bearer": []}], "description": "更新当前登录用户的用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["认证模块"], "summary": "更新用户信息", "parameters": [{"description": "更新参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.AuthUserinfoUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/apps": {"get": {"security": [{"Bearer": []}], "description": "分页获取应用列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "应用管理"], "summary": "获取应用列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "名称筛选", "name": "name", "in": "query"}, {"type": "string", "description": "别名筛选", "name": "slug", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.BusinessAppResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的应用记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "应用管理"], "summary": "创建应用", "parameters": [{"description": "应用信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessAppCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "应用管理"], "summary": "批量删除应用", "parameters": [{"description": "应用IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/apps/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的应用信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "应用管理"], "summary": "获取应用", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.BusinessAppResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的应用", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "应用管理"], "summary": "删除应用", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的应用信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "应用管理"], "summary": "更新应用", "parameters": [{"type": "integer", "description": "应用ID", "name": "id", "in": "path", "required": true}, {"description": "应用信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessAppUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/companies": {"get": {"security": [{"Bearer": []}], "description": "分页获取企业列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "企业管理"], "summary": "获取企业列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "企业名称筛选", "name": "name", "in": "query"}, {"type": "integer", "description": "行业筛选", "name": "industry", "in": "query"}, {"type": "integer", "description": "类型筛选", "name": "type", "in": "query"}, {"type": "integer", "description": "规模筛选", "name": "size", "in": "query"}, {"type": "integer", "description": "资质筛选", "name": "cert", "in": "query"}, {"type": "string", "description": "地区筛选", "name": "area", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.BusinessCompanyResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的企业记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "企业管理"], "summary": "创建企业", "parameters": [{"description": "企业信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessCompanyCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的企业", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "企业管理"], "summary": "批量删除企业", "parameters": [{"description": "企业IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/companies/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的企业信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "企业管理"], "summary": "获取企业", "parameters": [{"type": "integer", "description": "企业ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.BusinessCompanyResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的企业", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "企业管理"], "summary": "删除企业", "parameters": [{"type": "integer", "description": "企业ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的企业信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "企业管理"], "summary": "更新企业", "parameters": [{"type": "integer", "description": "企业ID", "name": "id", "in": "path", "required": true}, {"description": "企业信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessCompanyUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/experts": {"get": {"security": [{"Bearer": []}], "description": "分页获取专家列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "专家管理"], "summary": "获取专家列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "姓名筛选", "name": "name", "in": "query"}, {"type": "integer", "description": "性别筛选", "name": "gender", "in": "query"}, {"type": "string", "description": "地区筛选", "name": "area", "in": "query"}, {"type": "boolean", "description": "党员筛选", "name": "party", "in": "query"}, {"type": "string", "description": "擅长领域筛选", "name": "type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.BusinessExpertResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的专家记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "专家管理"], "summary": "创建专家", "parameters": [{"description": "专家信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessExpertCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的专家", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "专家管理"], "summary": "批量删除专家", "parameters": [{"description": "专家IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/experts/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的专家信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "专家管理"], "summary": "获取专家", "parameters": [{"type": "integer", "description": "专家ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.BusinessExpertResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的专家", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "专家管理"], "summary": "删除专家", "parameters": [{"type": "integer", "description": "专家ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的专家信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "专家管理"], "summary": "更新专家", "parameters": [{"type": "integer", "description": "专家ID", "name": "id", "in": "path", "required": true}, {"description": "专家信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessExpertUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/jobs": {"get": {"security": [{"Bearer": []}], "description": "分页获取招聘列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "招聘管理"], "summary": "获取招聘列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "职位名称筛选", "name": "title", "in": "query"}, {"type": "integer", "description": "公司ID筛选", "name": "company_id", "in": "query"}, {"type": "string", "description": "地区筛选", "name": "area", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.BusinessJobResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的招聘记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "招聘管理"], "summary": "创建招聘", "parameters": [{"description": "招聘信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessJobCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的招聘", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "招聘管理"], "summary": "批量删除招聘", "parameters": [{"description": "招聘IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/jobs/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的招聘信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "招聘管理"], "summary": "获取招聘", "parameters": [{"type": "integer", "description": "招聘ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.BusinessJobResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的招聘", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "招聘管理"], "summary": "删除招聘", "parameters": [{"type": "integer", "description": "招聘ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的招聘信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "招聘管理"], "summary": "更新招聘", "parameters": [{"type": "integer", "description": "招聘ID", "name": "id", "in": "path", "required": true}, {"description": "招聘信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessJobUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/knowledges": {"get": {"security": [{"Bearer": []}], "description": "分页获取知识列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "知识管理"], "summary": "获取知识列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "行业筛选", "name": "industry", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.BusinessKnowledgeResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的知识记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "知识管理"], "summary": "创建知识", "parameters": [{"description": "知识信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessKnowledgeCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的知识", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "知识管理"], "summary": "批量删除知识", "parameters": [{"description": "知识IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/knowledges/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的知识信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "知识管理"], "summary": "获取知识", "parameters": [{"type": "integer", "description": "知识ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.BusinessKnowledgeResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的知识", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "知识管理"], "summary": "删除知识", "parameters": [{"type": "integer", "description": "知识ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的知识信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "知识管理"], "summary": "更新知识", "parameters": [{"type": "integer", "description": "知识ID", "name": "id", "in": "path", "required": true}, {"description": "知识信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessKnowledgeUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/products": {"get": {"security": [{"Bearer": []}], "description": "分页获取产品列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "产品管理"], "summary": "获取产品列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "公司ID筛选", "name": "company_id", "in": "query"}, {"type": "string", "description": "产品名称筛选", "name": "name", "in": "query"}, {"type": "string", "description": "产品编码筛选", "name": "code", "in": "query"}, {"type": "integer", "description": "产品类型筛选", "name": "type", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.BusinessProductResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的产品记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "产品管理"], "summary": "创建产品", "parameters": [{"description": "产品信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessProductCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的产品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "产品管理"], "summary": "批量删除产品", "parameters": [{"description": "产品IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/products/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的产品信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "产品管理"], "summary": "获取产品", "parameters": [{"type": "integer", "description": "产品ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.BusinessProductResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的产品", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "产品管理"], "summary": "删除产品", "parameters": [{"type": "integer", "description": "产品ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的产品信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "产品管理"], "summary": "更新产品", "parameters": [{"type": "integer", "description": "产品ID", "name": "id", "in": "path", "required": true}, {"description": "产品信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessProductUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/reports": {"get": {"security": [{"Bearer": []}], "description": "分页获取报告列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "报告管理"], "summary": "获取报告列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "string", "description": "标题筛选", "name": "title", "in": "query"}, {"type": "integer", "description": "公司ID筛选", "name": "companyId", "in": "query"}, {"type": "string", "description": "展开关联数据，支持: company", "name": "_expand", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.BusinessReportResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的报告记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "报告管理"], "summary": "创建报告", "parameters": [{"description": "报告信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessReportCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的报告", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "报告管理"], "summary": "批量删除报告", "parameters": [{"description": "报告IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/reports/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的报告信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "报告管理"], "summary": "获取报告", "parameters": [{"type": "integer", "description": "报告ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.BusinessReportResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的报告", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "报告管理"], "summary": "删除报告", "parameters": [{"type": "integer", "description": "报告ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的报告信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "报告管理"], "summary": "更新报告", "parameters": [{"type": "integer", "description": "报告ID", "name": "id", "in": "path", "required": true}, {"description": "报告信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessReportUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/talents": {"get": {"security": [{"Bearer": []}], "description": "分页获取人才列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "人才管理"], "summary": "获取人才列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "string", "description": "姓名筛选", "name": "name", "in": "query"}, {"type": "integer", "description": "性别筛选", "name": "gender", "in": "query"}, {"type": "string", "description": "地区筛选", "name": "area", "in": "query"}, {"type": "boolean", "description": "是否参与筛选", "name": "party", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.BusinessTalentResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的人才记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "人才管理"], "summary": "创建人才", "parameters": [{"description": "人才信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessTalentCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的人才", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "人才管理"], "summary": "批量删除人才", "parameters": [{"description": "人才IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/business/talents/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的人才信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "人才管理"], "summary": "获取人才", "parameters": [{"type": "integer", "description": "人才ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.BusinessTalentResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的人才", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "人才管理"], "summary": "删除人才", "parameters": [{"type": "integer", "description": "人才ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的人才信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["业务模块", "人才管理"], "summary": "更新人才", "parameters": [{"type": "integer", "description": "人才ID", "name": "id", "in": "path", "required": true}, {"description": "人才信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BusinessTalentUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/cms/metas": {"get": {"security": [{"Bearer": []}], "description": "分页获取栏目列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "获取栏目列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "父级ID筛选", "name": "parentId", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.CmsMetaResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的栏目记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "创建栏目", "parameters": [{"description": "栏目信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.CmsMetaCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的栏目", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "批量删除栏目", "parameters": [{"description": "栏目IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/cms/metas/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的栏目信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "获取栏目", "parameters": [{"type": "integer", "description": "栏目ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.CmsMetaResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的栏目", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "删除栏目", "parameters": [{"type": "integer", "description": "栏目ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的栏目信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "栏目管理"], "summary": "更新栏目", "parameters": [{"type": "integer", "description": "栏目ID", "name": "id", "in": "path", "required": true}, {"description": "栏目信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.CmsMetaUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/cms/posts": {"get": {"security": [{"Bearer": []}], "description": "分页获取文章列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "获取文章列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "integer", "description": "栏目ID筛选", "name": "metaId", "in": "query"}, {"type": "string", "description": "作者筛选", "name": "author", "in": "query"}, {"type": "string", "description": "标志筛选", "name": "flag", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.CmsPostResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的文章记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "创建文章", "parameters": [{"description": "文章信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.CmsPostCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的文章", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "批量删除文章", "parameters": [{"description": "文章IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/cms/posts/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的文章信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "获取文章", "parameters": [{"type": "integer", "description": "文章ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.CmsPostResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的文章", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "删除文章", "parameters": [{"type": "integer", "description": "文章ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的文章信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["内容模块", "文章管理"], "summary": "更新文章", "parameters": [{"type": "integer", "description": "文章ID", "name": "id", "in": "path", "required": true}, {"description": "文章信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.CmsPostUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/apis": {"get": {"security": [{"Bearer": []}], "description": "分页获取接口列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "获取接口列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysApiResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的接口记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "创建接口", "parameters": [{"description": "接口信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysApiCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的接口", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "批量删除接口", "parameters": [{"description": "接口IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/apis/refresh": {"get": {"security": [{"Bearer": []}], "description": "重置所有接口信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "刷新接口表", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/apis/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的接口信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "获取接口", "parameters": [{"type": "integer", "description": "接口ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysApiResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的接口", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "删除接口", "parameters": [{"type": "integer", "description": "接口ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的接口信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "接口管理"], "summary": "更新接口", "parameters": [{"type": "integer", "description": "接口ID", "name": "id", "in": "path", "required": true}, {"description": "接口信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysApiUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/configs": {"get": {"security": [{"Bearer": []}], "description": "分页获取配置列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "获取配置列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的配置记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "创建配置", "parameters": [{"description": "配置信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "批量删除配置", "parameters": [{"description": "配置IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/configs/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的配置信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "获取配置", "parameters": [{"type": "integer", "description": "配置ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的配置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "删除配置", "parameters": [{"type": "integer", "description": "配置ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的配置信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "配置管理"], "summary": "更新配置", "parameters": [{"type": "integer", "description": "配置ID", "name": "id", "in": "path", "required": true}, {"description": "配置信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/depts": {"get": {"security": [{"Bearer": []}], "description": "分页获取部门列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "部门管理"], "summary": "获取部门列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDeptResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的部门", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "部门管理"], "summary": "创建部门", "parameters": [{"description": "部门信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysDeptCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的部门", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "部门管理"], "summary": "批量删除部门", "parameters": [{"description": "部门IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/depts/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的部门信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "部门管理"], "summary": "获取部门", "parameters": [{"type": "integer", "description": "部门ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysDeptResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的部门", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "部门管理"], "summary": "删除部门", "parameters": [{"type": "integer", "description": "部门ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的部门信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "部门管理"], "summary": "更新部门", "parameters": [{"type": "integer", "description": "部门ID", "name": "id", "in": "path", "required": true}, {"description": "部门信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysDeptUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/dicts": {"get": {"security": [{"Bearer": []}], "description": "分页获取字典列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "获取字典列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDictResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的字典记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "创建字典", "parameters": [{"description": "字典信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysDictCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的字典", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "批量删除字典", "parameters": [{"description": "字典IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/dicts/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的字典信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "获取字典", "parameters": [{"type": "integer", "description": "字典ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysDictResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的字典", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "删除字典", "parameters": [{"type": "integer", "description": "字典ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的字典信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "字典管理"], "summary": "更新字典", "parameters": [{"type": "integer", "description": "字典ID", "name": "id", "in": "path", "required": true}, {"description": "字典信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysDictUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/logs": {"get": {"security": [{"Bearer": []}], "description": "分页获取日志列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "日志管理"], "summary": "获取日志列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "string", "description": "请求方法筛选", "name": "method", "in": "query"}, {"type": "integer", "description": "状态码筛选", "name": "code", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysLogResponse"}}}}]}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的日志", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "日志管理"], "summary": "批量删除日志", "parameters": [{"description": "日志IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/logs/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的日志信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "日志管理"], "summary": "获取日志", "parameters": [{"type": "integer", "description": "日志ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysLogResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的日志", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "日志管理"], "summary": "删除日志", "parameters": [{"type": "integer", "description": "日志ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/menus": {"get": {"security": [{"Bearer": []}], "description": "分页获取菜单列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "获取菜单列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}, {"type": "boolean", "description": "常量筛选", "name": "constant", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysMenuResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的菜单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "创建菜单", "parameters": [{"description": "菜单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysMenuCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的菜单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "批量删除菜单", "parameters": [{"description": "菜单IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/menus/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的菜单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "获取菜单", "parameters": [{"type": "integer", "description": "菜单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysMenuResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的菜单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "删除菜单", "parameters": [{"type": "integer", "description": "菜单ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的菜单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "菜单管理"], "summary": "更新菜单", "parameters": [{"type": "integer", "description": "菜单ID", "name": "id", "in": "path", "required": true}, {"description": "菜单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysMenuUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/roles": {"get": {"security": [{"Bearer": []}], "description": "分页获取角色列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "获取角色列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "创建角色", "parameters": [{"description": "角色信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "批量删除角色", "parameters": [{"description": "角色IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/roles/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的角色信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "获取角色", "parameters": [{"type": "integer", "description": "角色ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "删除角色", "parameters": [{"type": "integer", "description": "角色ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的角色信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "角色管理"], "summary": "更新角色", "parameters": [{"type": "integer", "description": "角色ID", "name": "id", "in": "path", "required": true}, {"description": "角色信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/users": {"get": {"security": [{"Bearer": []}], "description": "分页获取用户列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "获取用户列表", "parameters": [{"type": "integer", "description": "当前页码(默认值: 1)", "name": "_page", "in": "query"}, {"type": "integer", "description": "每页数量(默认值: 10)", "name": "_limit", "in": "query"}, {"type": "string", "description": "排序字段，多个字段用逗号分隔", "name": "_sort", "in": "query"}, {"type": "string", "description": "排序方式，asc或desc，多个方式用逗号分隔", "name": "_order", "in": "query"}, {"type": "string", "description": "全文搜索", "name": "q", "in": "query"}, {"type": "boolean", "description": "状态筛选", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"allOf": [{"$ref": "#/definitions/daisy-server_pkg_pagination.Result"}, {"type": "object", "properties": {"records": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysUserResponse"}}}}]}}}]}}}}, "post": {"security": [{"Bearer": []}], "description": "创建新的用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "创建用户", "parameters": [{"description": "用户信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysUserCreateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "delete": {"security": [{"Bearer": []}], "description": "批量删除指定IDs的用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "批量删除用户", "parameters": [{"description": "用户IDs", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.BatchDeleteParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/system/users/{id}": {"get": {"security": [{"Bearer": []}], "description": "获取指定ID的用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "获取用户", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/daisy-server_api_v1.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/daisy-server_api_v1.SysUserResponse"}}}]}}}}, "delete": {"security": [{"Bearer": []}], "description": "删除指定ID的用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "删除用户", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}, "patch": {"security": [{"Bearer": []}], "description": "更新指定ID的用户信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["系统模块", "用户管理"], "summary": "更新用户", "parameters": [{"type": "integer", "description": "用户ID", "name": "id", "in": "path", "required": true}, {"description": "用户信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.SysUserUpdateParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/upload/file": {"post": {"security": [{"Bearer": []}], "description": "上传文件到MinIO服务器", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["上传模块"], "summary": "上传文件", "parameters": [{"type": "file", "description": "文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/upload/image": {"post": {"security": [{"Bearer": []}], "description": "上传图片到MinIO服务器", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["上传模块"], "summary": "上传图片", "parameters": [{"type": "file", "description": "图片文件", "name": "file", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}, "/upload/remove": {"delete": {"security": [{"Bearer": []}], "description": "从MinIO服务器删除资源", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["上传模块"], "summary": "删除资源", "parameters": [{"description": "文件信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/daisy-server_api_v1.UploadRemoveParams"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/daisy-server_api_v1.Response"}}}}}}, "definitions": {"daisy-server_api_v1.AuthLoginData": {"type": "object", "properties": {"refreshToken": {"type": "string"}, "token": {"type": "string"}}}, "daisy-server_api_v1.AuthLoginParams": {"type": "object", "required": ["password", "username"], "properties": {"password": {"type": "string", "example": "123456"}, "username": {"type": "string", "example": "admin"}}}, "daisy-server_api_v1.AuthLoginResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {"$ref": "#/definitions/daisy-server_api_v1.AuthLoginData"}, "message": {"type": "string"}}}, "daisy-server_api_v1.AuthPasswordUpdateParams": {"type": "object", "required": ["oldpass", "password", "repass"], "properties": {"oldpass": {"type": "string", "example": "123456"}, "password": {"type": "string", "example": "654321"}, "repass": {"type": "string", "example": "654321"}}}, "daisy-server_api_v1.AuthRefreshParams": {"type": "object", "required": ["refreshToken"], "properties": {"refreshToken": {"type": "string", "example": "refreshToken"}}}, "daisy-server_api_v1.AuthUserinfoResponse": {"type": "object", "properties": {"avatar": {"type": "string"}, "createdAt": {"type": "string"}, "dept": {"description": "部门信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.SysDeptResponse"}]}, "deptId": {"type": "integer"}, "email": {"type": "string"}, "gender": {"type": "integer"}, "id": {"type": "integer"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "roleIds": {"type": "array", "items": {"type": "integer"}}, "roles": {"description": "角色列表", "type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysRoleResponse"}}, "status": {"type": "boolean"}, "updatedAt": {"type": "string"}, "userId": {"type": "string"}, "username": {"type": "string"}}}, "daisy-server_api_v1.AuthUserinfoUpdateParams": {"type": "object", "required": ["email"], "properties": {"avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "email": {"type": "string", "example": "<EMAIL>"}, "gender": {"type": "integer", "example": 1}, "nickname": {"type": "string", "example": "alan"}, "phone": {"type": "string", "example": "***********"}}}, "daisy-server_api_v1.BatchDeleteParams": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}}, "daisy-server_api_v1.BusinessAppCreateParams": {"type": "object", "required": ["name", "slug"], "properties": {"logo": {"type": "string", "example": "https://example.com/logo.jpg"}, "name": {"type": "string", "example": "应用名称"}, "order": {"type": "integer", "example": 1}, "slug": {"type": "string", "example": "app-slug"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "应用简介"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "url": {"type": "string", "example": "https://example.com/app"}}}, "daisy-server_api_v1.BusinessAppResponse": {"type": "object", "properties": {"createdAt": {"type": "string"}, "id": {"type": "integer"}, "logo": {"type": "string"}, "name": {"type": "string"}, "order": {"type": "integer"}, "slug": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "updatedAt": {"type": "string"}, "url": {"type": "string"}}}, "daisy-server_api_v1.BusinessAppUpdateParams": {"type": "object", "properties": {"logo": {"type": "string", "example": "https://example.com/logo.jpg"}, "name": {"type": "string", "example": "应用名称"}, "order": {"type": "integer", "example": 1}, "slug": {"type": "string", "example": "app-slug"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "应用简介"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "url": {"type": "string", "example": "https://example.com/app"}}}, "daisy-server_api_v1.BusinessCompanyCreateParams": {"type": "object", "required": ["name"], "properties": {"address": {"type": "string", "example": "详细地址"}, "albums": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "area": {"type": "string", "example": "地区"}, "cert": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "contact": {"type": "string", "example": "联系人"}, "detail": {"type": "string", "example": "企业详情"}, "email": {"type": "string", "example": "邮箱"}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "industry": {"type": "integer", "example": 1}, "logo": {"type": "string", "example": "https://example.com/logo.jpg"}, "name": {"type": "string", "example": "企业名称"}, "order": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "电话号码"}, "size": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "企业简介"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "tianyanId": {"type": "string", "example": "123"}, "type": {"type": "integer", "example": 1}, "website": {"type": "string", "example": "网站地址"}}}, "daisy-server_api_v1.BusinessCompanyResponse": {"type": "object", "properties": {"address": {"type": "string"}, "albums": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "area": {"type": "string"}, "cert": {"type": "array", "items": {"type": "integer"}}, "contact": {"type": "string"}, "createdAt": {"type": "string"}, "detail": {"type": "string"}, "email": {"type": "string"}, "flag": {"type": "array", "items": {"type": "integer"}}, "id": {"type": "integer"}, "industry": {"type": "integer"}, "logo": {"type": "string"}, "name": {"type": "string"}, "order": {"type": "integer"}, "phone": {"type": "string"}, "size": {"type": "integer"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "tianyanId": {"type": "string"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}, "website": {"type": "string"}}}, "daisy-server_api_v1.BusinessCompanyUpdateParams": {"type": "object", "properties": {"address": {"type": "string", "example": "详细地址"}, "albums": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "area": {"type": "string", "example": "地区"}, "cert": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "contact": {"type": "string", "example": "联系人"}, "detail": {"type": "string", "example": "企业详情"}, "email": {"type": "string", "example": "邮箱"}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "industry": {"type": "integer", "example": 1}, "logo": {"type": "string", "example": "https://example.com/logo.jpg"}, "name": {"type": "string", "example": "企业名称"}, "order": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "电话号码"}, "size": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "企业简介"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "tianyanId": {"type": "string", "example": "123"}, "type": {"type": "integer", "example": 1}, "website": {"type": "string", "example": "网站地址"}}}, "daisy-server_api_v1.BusinessExpertCreateParams": {"type": "object", "required": ["name"], "properties": {"age": {"type": "integer", "example": 45}, "area": {"type": "string", "example": "北京"}, "avatar": {"type": "string", "example": "https://example.com/avatar.jpg"}, "detail": {"type": "string", "example": "专家详细介绍"}, "edu": {"type": "integer", "example": 4}, "email": {"type": "string", "example": "<EMAIL>"}, "exp": {"type": "integer", "example": 20}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "gender": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "张三"}, "order": {"type": "integer", "example": 1}, "party": {"type": "boolean", "example": true}, "phone": {"type": "string", "example": "***********"}, "skills": {"type": "array", "items": {"type": "string"}, "example": ["机械设计", "自动化", "控制系统"]}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "专家简介"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["高级", "首席"]}, "type": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}}, "daisy-server_api_v1.BusinessExpertResponse": {"type": "object", "properties": {"age": {"type": "integer"}, "area": {"type": "string"}, "avatar": {"type": "string"}, "createdAt": {"type": "string"}, "detail": {"type": "string"}, "edu": {"type": "integer"}, "email": {"type": "string"}, "exp": {"type": "integer"}, "flag": {"type": "array", "items": {"type": "integer"}}, "gender": {"type": "integer"}, "id": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "party": {"type": "boolean"}, "phone": {"type": "string"}, "skills": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "type": {"type": "array", "items": {"type": "integer"}}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.BusinessExpertUpdateParams": {"type": "object", "properties": {"age": {"type": "integer", "example": 45}, "area": {"type": "string", "example": "北京"}, "avatar": {"type": "string", "example": "https://example.com/avatar.jpg"}, "detail": {"type": "string", "example": "专家详细介绍"}, "edu": {"type": "integer", "example": 4}, "email": {"type": "string", "example": "<EMAIL>"}, "exp": {"type": "integer", "example": 20}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "gender": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "张三"}, "order": {"type": "integer", "example": 1}, "party": {"type": "boolean", "example": true}, "phone": {"type": "string", "example": "***********"}, "skills": {"type": "array", "items": {"type": "string"}, "example": ["机械设计", "自动化", "控制系统"]}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "专家简介"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["高级", "首席"]}, "type": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}}}, "daisy-server_api_v1.BusinessJobCreateParams": {"type": "object", "required": ["title"], "properties": {"area": {"type": "string", "example": "北京"}, "companyId": {"type": "integer", "example": 1}, "contact": {"type": "string", "example": "张三"}, "detail": {"type": "string", "example": "职位详情"}, "email": {"type": "string", "example": "<EMAIL>"}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "order": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "***********"}, "salary": {"type": "array", "items": {"type": "string"}, "example": ["5000", "10000"]}, "salaryType": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "example": "职位名称"}, "type": {"type": "integer", "example": 1}}}, "daisy-server_api_v1.BusinessJobResponse": {"type": "object", "properties": {"area": {"type": "string"}, "company": {"description": "企业信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.BusinessCompanyResponse"}]}, "companyId": {"type": "integer"}, "contact": {"type": "string"}, "createdAt": {"type": "string"}, "detail": {"type": "string"}, "email": {"type": "string"}, "flag": {"type": "array", "items": {"type": "integer"}}, "id": {"type": "integer"}, "order": {"type": "integer"}, "phone": {"type": "string"}, "salary": {"type": "array", "items": {"type": "integer"}}, "salaryType": {"type": "integer"}, "status": {"type": "boolean"}, "tags": {"type": "array", "items": {"type": "integer"}}, "title": {"type": "string"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.BusinessJobUpdateParams": {"type": "object", "properties": {"area": {"type": "string", "example": "北京"}, "companyId": {"type": "integer", "example": 1}, "contact": {"type": "string", "example": "张三"}, "detail": {"type": "string", "example": "职位详情"}, "email": {"type": "string", "example": "<EMAIL>"}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "order": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "***********"}, "salary": {"type": "array", "items": {"type": "string"}, "example": ["5000", "10000"]}, "salaryType": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "example": "职位名称"}, "type": {"type": "integer", "example": 1}}}, "daisy-server_api_v1.BusinessKnowledgeCreateParams": {"type": "object", "required": ["title"], "properties": {"content": {"type": "string", "example": "知识内容"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "industry": {"type": "integer", "example": 1}, "order": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "知识摘要"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "example": "知识标题"}}}, "daisy-server_api_v1.BusinessKnowledgeResponse": {"type": "object", "properties": {"content": {"type": "string"}, "cover": {"type": "string"}, "createdAt": {"type": "string"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}}, "id": {"type": "integer"}, "industry": {"type": "integer"}, "order": {"type": "integer"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "title": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.BusinessKnowledgeUpdateParams": {"type": "object", "properties": {"content": {"type": "string", "example": "知识内容"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "industry": {"type": "integer", "example": 1}, "order": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "知识摘要"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "example": "知识标题"}}}, "daisy-server_api_v1.BusinessProductCreateParams": {"type": "object", "required": ["code", "companyId", "name"], "properties": {"albums": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "code": {"type": "string", "example": "PRD-001"}, "companyId": {"type": "integer", "example": 1}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "detail": {"type": "string", "example": "产品详细描述"}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "material": {"type": "string", "example": "材料信息"}, "name": {"type": "string", "example": "产品名称"}, "order": {"type": "integer", "example": 1}, "spec": {"type": "string", "example": "规格信息"}, "standard": {"type": "string", "example": "标准信息"}, "status": {"type": "boolean", "example": true}, "strength": {"type": "string", "example": "强度信息"}, "surface": {"type": "string", "example": "表面处理"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "type": {"type": "integer", "example": 1}}}, "daisy-server_api_v1.BusinessProductResponse": {"type": "object", "properties": {"albums": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "code": {"type": "string"}, "company": {"description": "企业信息", "allOf": [{"$ref": "#/definitions/daisy-server_api_v1.BusinessCompanyResponse"}]}, "companyId": {"type": "integer"}, "cover": {"type": "string"}, "createdAt": {"type": "string"}, "detail": {"type": "string"}, "flag": {"type": "array", "items": {"type": "integer"}}, "id": {"type": "integer"}, "material": {"type": "string"}, "name": {"type": "string"}, "order": {"type": "integer"}, "spec": {"type": "string"}, "standard": {"type": "string"}, "status": {"type": "boolean"}, "strength": {"type": "string"}, "surface": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.BusinessProductUpdateParams": {"type": "object", "properties": {"albums": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "code": {"type": "string", "example": "PRD-001"}, "companyId": {"type": "integer", "example": 1}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "detail": {"type": "string", "example": "产品详细描述"}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "material": {"type": "string", "example": "材料信息"}, "name": {"type": "string", "example": "产品名称"}, "order": {"type": "integer", "example": 1}, "spec": {"type": "string", "example": "规格信息"}, "standard": {"type": "string", "example": "标准信息"}, "status": {"type": "boolean", "example": true}, "strength": {"type": "string", "example": "强度信息"}, "surface": {"type": "string", "example": "表面处理"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "type": {"type": "integer", "example": 1}}}, "daisy-server_api_v1.BusinessReportCreateParams": {"type": "object", "required": ["title"], "properties": {"companyId": {"type": "integer", "example": 1}, "content": {"type": "string", "example": "报告内容"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "order": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "报告摘要"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "example": "报告标题"}}}, "daisy-server_api_v1.BusinessReportResponse": {"type": "object", "properties": {"company": {"$ref": "#/definitions/daisy-server_api_v1.BusinessCompanyResponse"}, "companyId": {"type": "integer"}, "content": {"type": "string"}, "cover": {"type": "string"}, "createdAt": {"type": "string"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}}, "id": {"type": "integer"}, "order": {"type": "integer"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "title": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.BusinessReportUpdateParams": {"type": "object", "properties": {"companyId": {"type": "integer", "example": 1}, "content": {"type": "string", "example": "报告内容"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "order": {"type": "integer", "example": 1}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "报告摘要"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "example": "报告标题"}}}, "daisy-server_api_v1.BusinessTalentCreateParams": {"type": "object", "required": ["name"], "properties": {"address": {"type": "string", "example": "上海市浦东新区张江高科技园区"}, "age": {"type": "integer", "example": 28}, "area": {"type": "string", "example": "上海"}, "avatar": {"type": "string", "example": "https://example.com/avatar.jpg"}, "detail": {"type": "string", "example": "个人详细介绍"}, "edu": {"type": "integer", "example": 4}, "email": {"type": "string", "example": "<EMAIL>"}, "exp": {"type": "integer", "example": 5}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "gender": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "张三"}, "order": {"type": "integer", "example": 1}, "party": {"type": "boolean", "example": true}, "phone": {"type": "string", "example": "***********"}, "skills": {"type": "array", "items": {"type": "string"}, "example": ["Java", "Go", "Python"]}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "人才简介"}, "type": {"type": "integer", "example": 1}}}, "daisy-server_api_v1.BusinessTalentResponse": {"type": "object", "properties": {"address": {"type": "string"}, "age": {"type": "integer"}, "area": {"type": "string"}, "avatar": {"type": "string"}, "createdAt": {"type": "string"}, "detail": {"type": "string"}, "edu": {"type": "integer"}, "email": {"type": "string"}, "exp": {"type": "integer"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}}, "gender": {"type": "integer"}, "id": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "party": {"type": "boolean"}, "phone": {"type": "string"}, "skills": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "type": {"type": "integer"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.BusinessTalentUpdateParams": {"type": "object", "properties": {"address": {"type": "string", "example": "上海市浦东新区张江高科技园区"}, "age": {"type": "integer", "example": 28}, "area": {"type": "string", "example": "上海"}, "avatar": {"type": "string", "example": "https://example.com/avatar.jpg"}, "detail": {"type": "string", "example": "个人详细介绍"}, "edu": {"type": "integer", "example": 4}, "email": {"type": "string", "example": "<EMAIL>"}, "exp": {"type": "integer", "example": 5}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "gender": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "张三"}, "order": {"type": "integer", "example": 1}, "party": {"type": "boolean", "example": true}, "phone": {"type": "string", "example": "***********"}, "skills": {"type": "array", "items": {"type": "string"}, "example": ["Java", "Go", "Python"]}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "人才简介"}, "type": {"type": "integer", "example": 1}}}, "daisy-server_api_v1.CmsMetaCreateParams": {"type": "object", "required": ["name", "slug"], "properties": {"cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "name": {"type": "string", "example": "技术文章"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "slug": {"type": "string", "example": "tech"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "技术相关文章"}}}, "daisy-server_api_v1.CmsMetaResponse": {"type": "object", "properties": {"cover": {"type": "string"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "parentId": {"type": "integer"}, "slug": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.CmsMetaUpdateParams": {"type": "object", "properties": {"cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "name": {"type": "string", "example": "技术文章"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "slug": {"type": "string", "example": "tech"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "技术相关文章"}}}, "daisy-server_api_v1.CmsPostCreateParams": {"type": "object", "required": ["title"], "properties": {"author": {"type": "string", "example": "作者名"}, "content": {"type": "string", "example": "文章内容"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "from": {"type": "string", "example": "来源"}, "metaId": {"type": "integer", "example": 1}, "order": {"type": "integer", "example": 1}, "password": {"type": "string", "example": "访问密码"}, "slug": {"type": "string", "example": "article-slug"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "文章摘要"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "example": "文章标题"}}}, "daisy-server_api_v1.CmsPostResponse": {"type": "object", "properties": {"author": {"type": "string"}, "content": {"type": "string"}, "cover": {"type": "string"}, "createdAt": {"type": "string"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}}, "from": {"type": "string"}, "id": {"type": "integer"}, "metaId": {"type": "integer"}, "order": {"type": "integer"}, "password": {"type": "string"}, "slug": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "title": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.CmsPostUpdateParams": {"type": "object", "properties": {"author": {"type": "string", "example": "作者名"}, "content": {"type": "string", "example": "文章内容"}, "cover": {"type": "string", "example": "https://example.com/cover.jpg"}, "files": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.UploadFileParams"}}, "flag": {"type": "array", "items": {"type": "integer"}, "example": [1, 2]}, "from": {"type": "string", "example": "来源"}, "metaId": {"type": "integer", "example": 1}, "order": {"type": "integer", "example": 1}, "password": {"type": "string", "example": "访问密码"}, "slug": {"type": "string", "example": "article-slug"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "文章摘要"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}, "title": {"type": "string", "example": "文章标题"}}}, "daisy-server_api_v1.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "daisy-server_api_v1.SysApiCreateParams": {"type": "object", "required": ["method", "path"], "properties": {"method": {"type": "string", "example": "GET"}, "path": {"type": "string", "example": "/system/user/list"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "获取用户列表"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}}}, "daisy-server_api_v1.SysApiResponse": {"type": "object", "properties": {"createdAt": {"type": "string"}, "id": {"type": "integer"}, "method": {"type": "string"}, "path": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "tags": {"type": "array", "items": {"type": "integer"}}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysApiUpdateParams": {"type": "object", "properties": {"method": {"type": "string", "example": "GET"}, "path": {"type": "string", "example": "/system/user/list"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "获取用户列表"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["标签1", "标签2"]}}}, "daisy-server_api_v1.SysConfigCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "example": "system"}, "name": {"type": "string", "example": "系统配置"}, "params": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigParam"}}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "系统基础配置"}}}, "daisy-server_api_v1.SysConfigParam": {"type": "object", "properties": {"code": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "value": {"type": "string"}}}, "daisy-server_api_v1.SysConfigResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "params": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigParam"}}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysConfigUpdateParams": {"type": "object", "properties": {"code": {"type": "string", "example": "system"}, "name": {"type": "string", "example": "系统配置"}, "params": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysConfigParam"}}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "系统基础配置"}}}, "daisy-server_api_v1.SysDeptCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "example": "tech"}, "name": {"type": "string", "example": "技术部"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "负责技术开发"}}}, "daisy-server_api_v1.SysDeptResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "order": {"type": "integer"}, "parentId": {"type": "integer"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysDeptUpdateParams": {"type": "object", "properties": {"code": {"type": "string", "example": "tech"}, "name": {"type": "string", "example": "技术部"}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "负责技术开发"}}}, "daisy-server_api_v1.SysDictCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "example": "gender"}, "name": {"type": "string", "example": "性别"}, "options": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDictOption"}}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "性别字典"}}}, "daisy-server_api_v1.SysDictOption": {"type": "object", "properties": {"id": {"type": "string"}, "label": {"type": "string"}, "status": {"type": "boolean"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "daisy-server_api_v1.SysDictResponse": {"type": "object", "properties": {"code": {"type": "string"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "options": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDictOption"}}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysDictUpdateParams": {"type": "object", "properties": {"code": {"type": "string", "example": "gender"}, "name": {"type": "string", "example": "性别"}, "options": {"type": "array", "items": {"$ref": "#/definitions/daisy-server_api_v1.SysDictOption"}}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "性别字典"}}}, "daisy-server_api_v1.SysLogResponse": {"type": "object", "properties": {"clientIp": {"type": "string"}, "code": {"type": "integer"}, "createdAt": {"type": "string"}, "id": {"type": "integer"}, "method": {"type": "string"}, "params": {"type": "array", "items": {"type": "integer"}}, "path": {"type": "string"}, "time": {"type": "number"}, "updatedAt": {"type": "string"}, "userAgent": {"type": "string"}, "userId": {"type": "string"}}}, "daisy-server_api_v1.SysMenuCreateParams": {"type": "object", "required": ["menuName", "menuType"], "properties": {"component": {"type": "string", "example": "UserList"}, "constant": {"type": "boolean", "example": false}, "hideInMenu": {"type": "boolean", "example": false}, "href": {"type": "string", "example": "https://example.com"}, "icon": {"type": "string", "example": "user"}, "keepAlive": {"type": "boolean", "example": false}, "layout": {"type": "string", "example": "basic"}, "menuName": {"type": "string", "example": "用户管理"}, "menuType": {"type": "integer", "example": 0}, "multiTab": {"type": "boolean", "example": false}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "routeName": {"type": "string", "example": "user"}, "routePath": {"type": "string", "example": "/user"}, "status": {"type": "boolean", "example": true}}}, "daisy-server_api_v1.SysMenuResponse": {"type": "object", "properties": {"component": {"type": "string"}, "constant": {"type": "boolean"}, "createdAt": {"type": "string"}, "hideInMenu": {"type": "boolean"}, "href": {"type": "string"}, "icon": {"type": "string"}, "id": {"type": "integer"}, "keepAlive": {"type": "boolean"}, "layout": {"type": "string"}, "menuName": {"type": "string"}, "menuType": {"type": "integer"}, "multiTab": {"type": "boolean"}, "order": {"type": "integer"}, "parentId": {"type": "integer"}, "routeName": {"type": "string"}, "routePath": {"type": "string"}, "status": {"type": "boolean"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysMenuUpdateParams": {"type": "object", "properties": {"component": {"type": "string", "example": "UserList"}, "constant": {"type": "boolean", "example": false}, "hideInMenu": {"type": "boolean", "example": false}, "href": {"type": "string", "example": "https://example.com"}, "icon": {"type": "string", "example": "user"}, "keepAlive": {"type": "boolean", "example": false}, "layout": {"type": "string", "example": "basic"}, "menuName": {"type": "string", "example": "用户管理"}, "menuType": {"type": "integer", "example": 0}, "multiTab": {"type": "boolean", "example": false}, "order": {"type": "integer", "example": 1}, "parentId": {"type": "integer", "example": 0}, "routeName": {"type": "string", "example": "user"}, "routePath": {"type": "string", "example": "/user"}, "status": {"type": "boolean", "example": true}}}, "daisy-server_api_v1.SysRoleCreateParams": {"type": "object", "required": ["code", "name"], "properties": {"code": {"type": "string", "example": "admin"}, "name": {"type": "string", "example": "管理员"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "系统管理员角色"}}}, "daisy-server_api_v1.SysRoleResponse": {"type": "object", "properties": {"apiIds": {"type": "array", "items": {"type": "integer"}}, "code": {"type": "string"}, "createdAt": {"type": "string"}, "home": {"type": "string"}, "id": {"type": "integer"}, "menuIds": {"type": "array", "items": {"type": "integer"}}, "name": {"type": "string"}, "status": {"type": "boolean"}, "summary": {"type": "string"}, "updatedAt": {"type": "string"}}}, "daisy-server_api_v1.SysRoleUpdateParams": {"type": "object", "properties": {"apiIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "code": {"type": "string", "example": "admin"}, "home": {"type": "string", "example": "home"}, "menuIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "name": {"type": "string", "example": "管理员"}, "status": {"type": "boolean", "example": true}, "summary": {"type": "string", "example": "系统管理员角色"}}}, "daisy-server_api_v1.SysUserCreateParams": {"type": "object", "required": ["nickname", "password", "username"], "properties": {"avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "deptId": {"type": "integer", "example": 1}, "email": {"type": "string", "example": "<EMAIL>"}, "gender": {"type": "integer", "example": 0}, "nickname": {"type": "string", "example": "管理员"}, "password": {"type": "string", "example": "123456"}, "phone": {"type": "string", "example": "***********"}, "roleIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "username": {"type": "string", "example": "admin"}}}, "daisy-server_api_v1.SysUserResponse": {"type": "object", "properties": {"avatar": {"type": "string"}, "createdAt": {"type": "string"}, "deptId": {"type": "integer"}, "email": {"type": "string"}, "gender": {"type": "integer"}, "id": {"type": "integer"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "roleIds": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "boolean"}, "updatedAt": {"type": "string"}, "userId": {"type": "string"}, "username": {"type": "string"}}}, "daisy-server_api_v1.SysUserUpdateParams": {"type": "object", "properties": {"avatar": {"type": "string", "example": "https://example.com/avatar.png"}, "deptId": {"type": "integer", "example": 1}, "email": {"type": "string", "example": "<EMAIL>"}, "gender": {"type": "integer", "example": 0}, "nickname": {"type": "string", "example": "管理员"}, "password": {"type": "string", "example": "123456"}, "phone": {"type": "string", "example": "***********"}, "roleIds": {"type": "array", "items": {"type": "integer"}, "example": [1, 2, 3]}, "status": {"type": "boolean", "example": true}, "username": {"type": "string", "example": "admin"}}}, "daisy-server_api_v1.UploadFileParams": {"type": "object", "properties": {"id": {"type": "string", "example": "123456"}, "name": {"type": "string", "example": "文件名称"}, "size": {"type": "integer", "example": 1024}, "type": {"type": "string", "example": "application/pdf"}, "url": {"type": "string", "example": "https://example.com/file.pdf"}}}, "daisy-server_api_v1.UploadRemoveParams": {"type": "object", "required": ["fileName"], "properties": {"fileName": {"type": "string", "example": "images/123456.jpg"}}}, "daisy-server_pkg_pagination.Result": {"type": "object", "properties": {"records": {"description": "数据列表"}, "total": {"description": "总记录数", "type": "integer"}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}