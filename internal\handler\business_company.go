package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BusinessCompanyHandler struct {
	*Handler
	businessCompanyService service.BusinessCompanyService
}

func NewBusinessCompanyHandler(
	handler *Handler,
	businessCompanyService service.BusinessCompanyService,
) *BusinessCompanyHandler {
	return &BusinessCompanyHandler{
		Handler:                handler,
		businessCompanyService: businessCompanyService,
	}
}

// Create godoc
// @Summary 创建企业
// @Schemes
// @Description 创建新的企业记录
// @Tags 业务模块,企业管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BusinessCompanyCreateParams true "企业信息"
// @Success 200 {object} v1.Response
// @Router /business/companies [post]
func (h *BusinessCompanyHandler) Create(ctx *gin.Context) {
	var req v1.BusinessCompanyCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessCompanyService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新企业
// @Schemes
// @Description 更新指定ID的企业信息
// @Tags 业务模块,企业管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "企业ID"
// @Param request body v1.BusinessCompanyUpdateParams true "企业信息"
// @Success 200 {object} v1.Response
// @Router /business/companies/{id} [patch]
func (h *BusinessCompanyHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.BusinessCompanyUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessCompanyService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除企业
// @Schemes
// @Description 删除指定ID的企业
// @Tags 业务模块,企业管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "企业ID"
// @Success 200 {object} v1.Response
// @Router /business/companies/{id} [delete]
func (h *BusinessCompanyHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessCompanyService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除企业
// @Schemes
// @Description 批量删除指定IDs的企业
// @Tags 业务模块,企业管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "企业IDs"
// @Success 200 {object} v1.Response
// @Router /business/companies [delete]
func (h *BusinessCompanyHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessCompanyService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取企业
// @Schemes
// @Description 获取指定ID的企业信息
// @Tags 业务模块,企业管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "企业ID"
// @Success 200 {object} v1.Response{data=v1.BusinessCompanyResponse}
// @Router /business/companies/{id} [get]
func (h *BusinessCompanyHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	company, err := h.businessCompanyService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, company)
}

// List godoc
// @Summary 获取企业列表
// @Schemes
// @Description 分页获取企业列表
// @Tags 业务模块,企业管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param name query string false "企业名称筛选" example:"企业名称"
// @Param industry query int false "行业筛选" example:"1"
// @Param type query int false "类型筛选" example:"1"
// @Param size query int false "规模筛选" example:"1"
// @Param cert query int false "资质筛选" example:"1"
// @Param area query string false "地区筛选" example:"北京"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.BusinessCompanyResponse}}
// @Router /business/companies [get]
func (h *BusinessCompanyHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.businessCompanyService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
