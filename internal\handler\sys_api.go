package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
)

type SysApiHandler struct {
	*Handler
	sysApiService service.SysApiService
	conf          *viper.Viper
}

func NewSysApiHandler(
	handler *Handler,
	sysApiService service.SysApiService,
	conf *viper.Viper,
) *SysApiHandler {
	return &SysApiHandler{
		Handler:       handler,
		sysApiService: sysApiService,
		conf:          conf,
	}
}

// Create godoc
// @Summary 创建接口
// @Schemes
// @Description 创建新的接口记录
// @Tags 系统模块,接口管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.SysApiCreateParams true "接口信息"
// @Success 200 {object} v1.Response
// @Router /system/apis [post]
func (h *SysApiHandler) Create(ctx *gin.Context) {
	var req v1.SysApiCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysApiService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新接口
// @Schemes
// @Description 更新指定ID的接口信息
// @Tags 系统模块,接口管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "接口ID"
// @Param request body v1.SysApiUpdateParams true "接口信息"
// @Success 200 {object} v1.Response
// @Router /system/apis/{id} [patch]
func (h *SysApiHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.SysApiUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysApiService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除接口
// @Schemes
// @Description 删除指定ID的接口
// @Tags 系统模块,接口管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "接口ID"
// @Success 200 {object} v1.Response
// @Router /system/apis/{id} [delete]
func (h *SysApiHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysApiService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除接口
// @Schemes
// @Description 批量删除指定IDs的接口
// @Tags 系统模块,接口管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "接口IDs"
// @Success 200 {object} v1.Response
// @Router /system/apis [delete]
func (h *SysApiHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysApiService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取接口
// @Schemes
// @Description 获取指定ID的接口信息
// @Tags 系统模块,接口管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "接口ID"
// @Success 200 {object} v1.Response{data=v1.SysApiResponse}
// @Router /system/apis/{id} [get]
func (h *SysApiHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	api, err := h.sysApiService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, api)
}

// List godoc
// @Summary 获取接口列表
// @Schemes
// @Description 分页获取接口列表
// @Tags 系统模块,接口管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysApiResponse}}
// @Router /system/apis [get]
func (h *SysApiHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.sysApiService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}

// Refresh godoc
// @Summary 刷新接口表
// @Schemes
// @Description 重置所有接口信息
// @Tags 系统模块,接口管理
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} v1.Response
// @Router /system/apis/refresh [get]
func (h *SysApiHandler) Refresh(ctx *gin.Context) {
	env := h.conf.GetString("env")
	if env == "prod" {
		v1.HandleError(ctx, http.StatusForbidden, nil, "生产环境禁止刷新接口表")
		return
	}
	if err := h.sysApiService.RefreshFromSwagger(ctx, "docs/swagger.json"); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, "刷新接口表失败")
		return
	}
	v1.HandleSuccess(ctx, nil)
}
