package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type BusinessApp struct {
	gorm.Model
	Name    string         `gorm:"size:255; not null; index; comment:应用名称"`
	Slug    string         `gorm:"size:255; not null; unique; comment:应用别名"`
	Logo    string         `gorm:"size:255; comment:应用logo"`
	Summary string         `gorm:"size:255; comment:应用简介"`
	Url     string         `gorm:"size:255; comment:应用地址"`
	Tags    datatypes.JSON `gorm:"type:jsonb; comment:标签"`
	Order   int            `gorm:"default:0; index; comment:排序"`
	Status  bool           `gorm:"default:false; index; comment:状态"`
}

func (m *BusinessApp) TableName() string {
	return "business_app"
}
