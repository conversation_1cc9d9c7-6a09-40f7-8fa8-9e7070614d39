package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

type SysRoleService interface {
	Create(ctx context.Context, req *v1.SysRoleCreateParams) error
	Update(ctx context.Context, id uint, req *v1.SysRoleUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.SysRoleResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewSysRoleService(
	service *Service,
	sysRoleRepository repository.SysRoleRepository,
) SysRoleService {
	return &sysRoleService{
		Service:           service,
		sysRoleRepository: sysRoleRepository,
	}
}

type sysRoleService struct {
	*Service
	sysRoleRepository repository.SysRoleRepository
}

// 角色相关方法实现
func (s *sysRoleService) Create(ctx context.Context, req *v1.SysRoleCreateParams) error {
	role := &model.SysRole{}
	if err := copier.Copy(role, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysRoleRepository.Create(ctx, role); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysRoleService) Update(ctx context.Context, id uint, req *v1.SysRoleUpdateParams) error {
	role, err := s.sysRoleRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(role, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysRoleRepository.Update(ctx, role); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysRoleService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysRoleRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysRoleService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.sysRoleRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *sysRoleService) Get(ctx context.Context, id uint) (*v1.SysRoleResponse, error) {
	role, err := s.sysRoleRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.SysRoleResponse{}
	if err := copier.Copy(response, role); err != nil {
		return nil, err
	}

	response.CreatedAt = role.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = role.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *sysRoleService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 编码筛选
	code := ctx.(*gin.Context).DefaultQuery("code", "")
	if code != "" {
		params.AddFilter("code", code)
	}

	roles, total, err := s.sysRoleRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 将原始记录转换为API响应格式
	records := make([]*v1.SysRoleResponse, 0, len(roles))
	for _, role := range roles {
		response := &v1.SysRoleResponse{}
		if err := copier.Copy(response, role); err != nil {
			return nil, err
		}

		response.CreatedAt = role.CreatedAt.Format(time.RFC3339)
		response.UpdatedAt = role.UpdatedAt.Format(time.RFC3339)

		records = append(records, response)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
