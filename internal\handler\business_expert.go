package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BusinessExpertHandler struct {
	*Handler
	businessExpertService service.BusinessExpertService
}

func NewBusinessExpertHandler(
	handler *Handler,
	businessExpertService service.BusinessExpertService,
) *BusinessExpertHandler {
	return &BusinessExpertHandler{
		Handler:               handler,
		businessExpertService: businessExpertService,
	}
}

// Create godoc
// @Summary 创建专家
// @Schemes
// @Description 创建新的专家记录
// @Tags 业务模块,专家管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BusinessExpertCreateParams true "专家信息"
// @Success 200 {object} v1.Response
// @Router /business/experts [post]
func (h *BusinessExpertHandler) Create(ctx *gin.Context) {
	var req v1.BusinessExpertCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessExpertService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新专家
// @Schemes
// @Description 更新指定ID的专家信息
// @Tags 业务模块,专家管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "专家ID"
// @Param request body v1.BusinessExpertUpdateParams true "专家信息"
// @Success 200 {object} v1.Response
// @Router /business/experts/{id} [patch]
func (h *BusinessExpertHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.BusinessExpertUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessExpertService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除专家
// @Schemes
// @Description 删除指定ID的专家
// @Tags 业务模块,专家管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "专家ID"
// @Success 200 {object} v1.Response
// @Router /business/experts/{id} [delete]
func (h *BusinessExpertHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessExpertService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除专家
// @Schemes
// @Description 批量删除指定IDs的专家
// @Tags 业务模块,专家管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "专家IDs"
// @Success 200 {object} v1.Response
// @Router /business/experts [delete]
func (h *BusinessExpertHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessExpertService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取专家
// @Schemes
// @Description 获取指定ID的专家信息
// @Tags 业务模块,专家管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "专家ID"
// @Success 200 {object} v1.Response{data=v1.BusinessExpertResponse}
// @Router /business/experts/{id} [get]
func (h *BusinessExpertHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	expert, err := h.businessExpertService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, expert)
}

// List godoc
// @Summary 获取专家列表
// @Schemes
// @Description 分页获取专家列表
// @Tags 业务模块,专家管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param name query string false "姓名筛选" example:"张三"
// @Param gender query int false "性别筛选" example:"1"
// @Param area query string false "地区筛选" example:"北京"
// @Param party query bool false "党员筛选" example:"true"
// @Param type query string false "擅长领域筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.BusinessExpertResponse}}
// @Router /business/experts [get]
func (h *BusinessExpertHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.businessExpertService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
