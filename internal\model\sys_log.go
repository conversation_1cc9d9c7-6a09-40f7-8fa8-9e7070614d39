package model

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type SysLog struct {
	gorm.Model
	UserId    string         `gorm:"size:64; index; comment:用户ID"`
	Path      string         `gorm:"size:255; not null; comment:路径"`
	Method    string         `gorm:"size:255; not null; comment:方法"`
	Code      int            `gorm:"default:0; comment:状态码"`
	Params    datatypes.JSON `gorm:"type:jsonb; comment:请求参数"`
	Time      float64        `gorm:"default:0; comment:执行时间"`
	UserAgent string         `gorm:"size:255; comment:用户代理"`
	ClientIp  string         `gorm:"size:255; comment:客户端IP"`
}

func (m *SysLog) TableName() string {
	return "sys_log"
}
