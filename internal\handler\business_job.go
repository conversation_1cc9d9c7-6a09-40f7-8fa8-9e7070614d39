package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BusinessJobHandler struct {
	*Handler
	businessJobService service.BusinessJobService
}

func NewBusinessJobHandler(
	handler *Handler,
	businessJobService service.BusinessJobService,
) *BusinessJobHandler {
	return &BusinessJobHandler{
		Handler:            handler,
		businessJobService: businessJobService,
	}
}

// Create godoc
// @Summary 创建招聘
// @Schemes
// @Description 创建新的招聘记录
// @Tags 业务模块,招聘管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BusinessJobCreateParams true "招聘信息"
// @Success 200 {object} v1.Response
// @Router /business/jobs [post]
func (h *BusinessJobHandler) Create(ctx *gin.Context) {
	var req v1.BusinessJobCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessJobService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新招聘
// @Schemes
// @Description 更新指定ID的招聘信息
// @Tags 业务模块,招聘管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "招聘ID"
// @Param request body v1.BusinessJobUpdateParams true "招聘信息"
// @Success 200 {object} v1.Response
// @Router /business/jobs/{id} [patch]
func (h *BusinessJobHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.BusinessJobUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessJobService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除招聘
// @Schemes
// @Description 删除指定ID的招聘
// @Tags 业务模块,招聘管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "招聘ID"
// @Success 200 {object} v1.Response
// @Router /business/jobs/{id} [delete]
func (h *BusinessJobHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessJobService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除招聘
// @Schemes
// @Description 批量删除指定IDs的招聘
// @Tags 业务模块,招聘管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "招聘IDs"
// @Success 200 {object} v1.Response
// @Router /business/jobs [delete]
func (h *BusinessJobHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessJobService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取招聘
// @Schemes
// @Description 获取指定ID的招聘信息
// @Tags 业务模块,招聘管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "招聘ID"
// @Success 200 {object} v1.Response{data=v1.BusinessJobResponse}
// @Router /business/jobs/{id} [get]
func (h *BusinessJobHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	job, err := h.businessJobService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, job)
}

// List godoc
// @Summary 获取招聘列表
// @Schemes
// @Description 分页获取招聘列表
// @Tags 业务模块,招聘管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param title query string false "职位名称筛选" example:"职位名称"
// @Param company_id query int false "公司ID筛选" example:"1"
// @Param area query string false "地区筛选" example:"北京"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.BusinessJobResponse}}
// @Router /business/jobs [get]
func (h *BusinessJobHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.businessJobService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
