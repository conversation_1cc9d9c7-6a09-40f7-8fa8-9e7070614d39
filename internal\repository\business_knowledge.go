package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type BusinessKnowledgeRepository interface {
	Create(ctx context.Context, knowledge *model.BusinessKnowledge) error
	Update(ctx context.Context, knowledge *model.BusinessKnowledge) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.BusinessKnowledge, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.BusinessKnowledge, int64, error)
}

func NewBusinessKnowledgeRepository(
	repository *Repository,
) BusinessKnowledgeRepository {
	return &businessKnowledgeRepository{
		Repository: repository,
	}
}

type businessKnowledgeRepository struct {
	*Repository
}

func (r *businessKnowledgeRepository) Create(ctx context.Context, knowledge *model.BusinessKnowledge) error {
	if err := r.DB(ctx).Create(knowledge).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessKnowledgeRepository) Update(ctx context.Context, knowledge *model.BusinessKnowledge) error {
	if err := r.DB(ctx).Save(knowledge).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessKnowledgeRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessKnowledge{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessKnowledgeRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessKnowledge{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessKnowledgeRepository) Get(ctx context.Context, id uint) (*model.BusinessKnowledge, error) {
	var knowledge model.BusinessKnowledge
	if err := r.DB(ctx).First(&knowledge, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &knowledge, nil
}

func (r *businessKnowledgeRepository) List(ctx context.Context, params *pagination.Params) ([]*model.BusinessKnowledge, int64, error) {
	var records []*model.BusinessKnowledge
	var total int64

	db := r.DB(ctx).Model(&model.BusinessKnowledge{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.BusinessKnowledge{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
