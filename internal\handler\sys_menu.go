package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SysMenuHandler struct {
	*Handler
	sysMenuService service.SysMenuService
}

func NewSysMenuHandler(
	handler *Handler,
	sysMenuService service.SysMenuService,
) *SysMenuHandler {
	return &SysMenuHandler{
		Handler:        handler,
		sysMenuService: sysMenuService,
	}
}

// Create godoc
// @Summary 创建菜单
// @Schemes
// @Description 创建新的菜单
// @Tags 系统模块,菜单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.SysMenuCreateParams true "菜单信息"
// @Success 200 {object} v1.Response
// @Router /system/menus [post]
func (h *SysMenuHandler) Create(ctx *gin.Context) {
	var req v1.SysMenuCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysMenuService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新菜单
// @Schemes
// @Description 更新指定ID的菜单信息
// @Tags 系统模块,菜单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "菜单ID"
// @Param request body v1.SysMenuUpdateParams true "菜单信息"
// @Success 200 {object} v1.Response
// @Router /system/menus/{id} [patch]
func (h *SysMenuHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.SysMenuUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysMenuService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除菜单
// @Schemes
// @Description 删除指定ID的菜单
// @Tags 系统模块,菜单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "菜单ID"
// @Success 200 {object} v1.Response
// @Router /system/menus/{id} [delete]
func (h *SysMenuHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysMenuService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除菜单
// @Schemes
// @Description 批量删除指定IDs的菜单
// @Tags 系统模块,菜单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "菜单IDs"
// @Success 200 {object} v1.Response
// @Router /system/menus [delete]
func (h *SysMenuHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysMenuService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取菜单
// @Schemes
// @Description 获取指定ID的菜单信息
// @Tags 系统模块,菜单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "菜单ID"
// @Success 200 {object} v1.Response{data=v1.SysMenuResponse}
// @Router /system/menus/{id} [get]
func (h *SysMenuHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	menu, err := h.sysMenuService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, menu)
}

// List godoc
// @Summary 获取菜单列表
// @Schemes
// @Description 分页获取菜单列表
// @Tags 系统模块,菜单管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param constant query bool false "常量筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysMenuResponse}}
// @Router /system/menus [get]
func (h *SysMenuHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.sysMenuService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
