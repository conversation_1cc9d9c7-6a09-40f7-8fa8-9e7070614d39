package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BusinessReportHandler struct {
	*Handler
	businessReportService service.BusinessReportService
}

func NewBusinessReportHandler(
	handler *Handler,
	businessReportService service.BusinessReportService,
) *BusinessReportHandler {
	return &BusinessReportHandler{
		Handler:               handler,
		businessReportService: businessReportService,
	}
}

// Create godoc
// @Summary 创建报告
// @Schemes
// @Description 创建新的报告记录
// @Tags 业务模块,报告管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BusinessReportCreateParams true "报告信息"
// @Success 200 {object} v1.Response
// @Router /business/reports [post]
func (h *BusinessReportHandler) Create(ctx *gin.Context) {
	var req v1.BusinessReportCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessReportService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新报告
// @Schemes
// @Description 更新指定ID的报告信息
// @Tags 业务模块,报告管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "报告ID"
// @Param request body v1.BusinessReportUpdateParams true "报告信息"
// @Success 200 {object} v1.Response
// @Router /business/reports/{id} [patch]
func (h *BusinessReportHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.BusinessReportUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessReportService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除报告
// @Schemes
// @Description 删除指定ID的报告
// @Tags 业务模块,报告管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "报告ID"
// @Success 200 {object} v1.Response
// @Router /business/reports/{id} [delete]
func (h *BusinessReportHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessReportService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除报告
// @Schemes
// @Description 批量删除指定IDs的报告
// @Tags 业务模块,报告管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "报告IDs"
// @Success 200 {object} v1.Response
// @Router /business/reports [delete]
func (h *BusinessReportHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessReportService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取报告
// @Schemes
// @Description 获取指定ID的报告信息
// @Tags 业务模块,报告管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "报告ID"
// @Success 200 {object} v1.Response{data=v1.BusinessReportResponse}
// @Router /business/reports/{id} [get]
func (h *BusinessReportHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	report, err := h.businessReportService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, report)
}

// List godoc
// @Summary 获取报告列表
// @Schemes
// @Description 分页获取报告列表
// @Tags 业务模块,报告管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param title query string false "标题筛选" example:"报告标题"
// @Param companyId query int false "公司ID筛选" example:"1"
// @Param _expand query string false "展开关联数据，支持: company" example:"company"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.BusinessReportResponse}}
// @Router /business/reports [get]
func (h *BusinessReportHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.businessReportService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
