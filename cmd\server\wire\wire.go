//go:build wireinject
// +build wireinject

package wire

import (
	"daisy-server/internal/handler"
	"daisy-server/internal/job"
	"daisy-server/internal/repository"
	"daisy-server/internal/server"
	"daisy-server/internal/service"
	"daisy-server/pkg/app"
	"daisy-server/pkg/jwt"
	"daisy-server/pkg/log"
	"daisy-server/pkg/rbac"
	"daisy-server/pkg/server/http"
	"daisy-server/pkg/sid"

	"github.com/google/wire"
	"github.com/spf13/viper"
)

var repositorySet = wire.NewSet(
	repository.NewDB,
	repository.NewRedis,
	//repository.NewMongo,
	repository.NewRepository,
	repository.NewTransaction,
	repository.NewSysUserRepository,
	repository.NewSysRoleRepository,
	repository.NewSysDeptRepository,
	repository.NewSysMenuRepository,
	repository.NewSysLogRepository,
	repository.NewSysApiRepository,
	repository.NewSysDictRepository,
	repository.NewSysConfigRepository,
	repository.NewCmsMetaRepository,
	repository.NewCmsPostRepository,
	repository.NewBusinessCompanyRepository,
	repository.NewBusinessProductRepository,
	repository.NewBusinessTalentRepository,
	repository.NewBusinessExpertRepository,
	repository.NewBusinessKnowledgeRepository,
	repository.NewBusinessAppRepository,
	repository.NewBusinessReportRepository,
	repository.NewBusinessJobRepository,
)

var serviceSet = wire.NewSet(
	service.NewService,
	service.NewAuthService,
	service.NewUploadService,
	service.NewSysUserService,
	service.NewSysRoleService,
	service.NewSysDeptService,
	service.NewSysMenuService,
	service.NewSysLogService,
	service.NewSysApiService,
	service.NewSysDictService,
	service.NewSysConfigService,
	service.NewCmsMetaService,
	service.NewCmsPostService,
	service.NewBusinessCompanyService,
	service.NewBusinessProductService,
	service.NewBusinessTalentService,
	service.NewBusinessExpertService,
	service.NewBusinessKnowledgeService,
	service.NewBusinessAppService,
	service.NewBusinessReportService,
	service.NewBusinessJobService,
)

var handlerSet = wire.NewSet(
	handler.NewHandler,
	handler.NewAuthHandler,
	handler.NewUploadHandler,
	handler.NewSysUserHandler,
	handler.NewSysRoleHandler,
	handler.NewSysDeptHandler,
	handler.NewSysMenuHandler,
	handler.NewSysLogHandler,
	handler.NewSysApiHandler,
	handler.NewSysDictHandler,
	handler.NewSysConfigHandler,
	handler.NewCmsMetaHandler,
	handler.NewCmsPostHandler,
	handler.NewBusinessCompanyHandler,
	handler.NewBusinessProductHandler,
	handler.NewBusinessTalentHandler,
	handler.NewBusinessExpertHandler,
	handler.NewBusinessKnowledgeHandler,
	handler.NewBusinessAppHandler,
	handler.NewBusinessReportHandler,
	handler.NewBusinessJobHandler,
)

var jobSet = wire.NewSet(
	job.NewJob,
	job.NewUserJob,
)
var serverSet = wire.NewSet(
	server.NewHTTPServer,
	server.NewJobServer,
)

// build App
func newApp(
	httpServer *http.Server,
	jobServer *server.JobServer,
	// task *server.Task,
) *app.App {
	return app.NewApp(
		app.WithServer(httpServer, jobServer),
		app.WithName("daisy-server"),
	)
}

func NewWire(*viper.Viper, *log.Logger) (*app.App, func(), error) {
	panic(wire.Build(
		repositorySet,
		serviceSet,
		handlerSet,
		jobSet,
		serverSet,
		rbac.NewRBAC,
		sid.NewSid,
		jwt.NewJwt,
		newApp,
	))
}
