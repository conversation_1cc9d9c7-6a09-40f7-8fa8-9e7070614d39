package v1

import (
	"gorm.io/datatypes"
)

type BusinessAppCreateParams struct {
	Name    string         `json:"name" binding:"required" example:"应用名称"`
	Slug    string         `json:"slug" binding:"required" example:"app-slug"`
	Logo    string         `json:"logo" example:"https://example.com/logo.jpg"`
	Summary string         `json:"summary" example:"应用简介"`
	Url     string         `json:"url" example:"https://example.com/app"`
	Tags    datatypes.JSON `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Order   int            `json:"order" example:"1"`
	Status  bool           `json:"status" example:"true"`
}

type BusinessAppUpdateParams struct {
	Name    string         `json:"name" example:"应用名称"`
	Slug    string         `json:"slug" example:"app-slug"`
	Logo    string         `json:"logo" example:"https://example.com/logo.jpg"`
	Summary string         `json:"summary" example:"应用简介"`
	Url     string         `json:"url" example:"https://example.com/app"`
	Tags    datatypes.JSON `json:"tags" swaggertype:"array,string" example:"标签1,标签2"`
	Order   int            `json:"order" example:"1"`
	Status  bool           `json:"status" example:"true"`
}

type BusinessAppResponse struct {
	ID        uint           `json:"id"`
	Name      string         `json:"name"`
	Slug      string         `json:"slug"`
	Logo      string         `json:"logo"`
	Summary   string         `json:"summary"`
	Url       string         `json:"url"`
	Tags      datatypes.JSON `json:"tags"`
	Order     int            `json:"order"`
	Status    bool           `json:"status"`
	CreatedAt string         `json:"createdAt"`
	UpdatedAt string         `json:"updatedAt"`
}
