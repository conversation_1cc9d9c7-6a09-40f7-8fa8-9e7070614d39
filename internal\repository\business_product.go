package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type BusinessProductRepository interface {
	Create(ctx context.Context, product *model.BusinessProduct) error
	Update(ctx context.Context, product *model.BusinessProduct) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.BusinessProduct, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.BusinessProduct, int64, error)
}

func NewBusinessProductRepository(
	repository *Repository,
) BusinessProductRepository {
	return &businessProductRepository{
		Repository: repository,
	}
}

type businessProductRepository struct {
	*Repository
}

func (r *businessProductRepository) Create(ctx context.Context, product *model.BusinessProduct) error {
	if err := r.DB(ctx).Create(product).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessProductRepository) Update(ctx context.Context, product *model.BusinessProduct) error {
	if err := r.DB(ctx).Save(product).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessProductRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessProduct{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessProductRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessProduct{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessProductRepository) Get(ctx context.Context, id uint) (*model.BusinessProduct, error) {
	var product model.BusinessProduct
	if err := r.DB(ctx).First(&product, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &product, nil
}

func (r *businessProductRepository) List(ctx context.Context, params *pagination.Params) ([]*model.BusinessProduct, int64, error) {
	var records []*model.BusinessProduct
	var total int64

	db := r.DB(ctx).Model(&model.BusinessProduct{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.BusinessProduct{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
