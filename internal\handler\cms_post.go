package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CmsPostHandler struct {
	*Handler
	cmsPostService service.CmsPostService
}

func NewCmsPostHandler(
	handler *Handler,
	cmsPostService service.CmsPostService,
) *CmsPostHandler {
	return &CmsPostHandler{
		Handler:        handler,
		cmsPostService: cmsPostService,
	}
}

// Create godoc
// @Summary 创建文章
// @Schemes
// @Description 创建新的文章记录
// @Tags 内容模块,文章管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.CmsPostCreateParams true "文章信息"
// @Success 200 {object} v1.Response
// @Router /cms/posts [post]
func (h *CmsPostHandler) Create(ctx *gin.Context) {
	var req v1.CmsPostCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.cmsPostService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新文章
// @Schemes
// @Description 更新指定ID的文章信息
// @Tags 内容模块,文章管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "文章ID"
// @Param request body v1.CmsPostUpdateParams true "文章信息"
// @Success 200 {object} v1.Response
// @Router /cms/posts/{id} [patch]
func (h *CmsPostHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.CmsPostUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.cmsPostService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除文章
// @Schemes
// @Description 删除指定ID的文章
// @Tags 内容模块,文章管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "文章ID"
// @Success 200 {object} v1.Response
// @Router /cms/posts/{id} [delete]
func (h *CmsPostHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.cmsPostService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除文章
// @Schemes
// @Description 批量删除指定IDs的文章
// @Tags 内容模块,文章管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "文章IDs"
// @Success 200 {object} v1.Response
// @Router /cms/posts [delete]
func (h *CmsPostHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.cmsPostService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取文章
// @Schemes
// @Description 获取指定ID的文章信息
// @Tags 内容模块,文章管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "文章ID"
// @Success 200 {object} v1.Response{data=v1.CmsPostResponse}
// @Router /cms/posts/{id} [get]
func (h *CmsPostHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	post, err := h.cmsPostService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, post)
}

// List godoc
// @Summary 获取文章列表
// @Schemes
// @Description 分页获取文章列表
// @Tags 内容模块,文章管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param metaId query int false "栏目ID筛选" example:"1"
// @Param author query string false "作者筛选" example:"作者名"
// @Param flag query string false "标志筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.CmsPostResponse}}
// @Router /cms/posts [get]
func (h *CmsPostHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.cmsPostService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
