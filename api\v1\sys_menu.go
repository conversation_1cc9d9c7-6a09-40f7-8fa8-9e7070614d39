package v1

type SysMenuCreateParams struct {
	MenuType   int    `json:"menuType" binding:"required" example:"0"`
	MenuName   string `json:"menuName" binding:"required" example:"用户管理"`
	RouteName  string `json:"routeName" example:"user"`
	RoutePath  string `json:"routePath" example:"/user"`
	Layout     string `json:"layout" example:"basic"`
	Component  string `json:"component" example:"UserList"`
	Icon       string `json:"icon" example:"user"`
	Order      int    `json:"order" example:"1"`
	Href       string `json:"href" example:"https://example.com"`
	Constant   bool   `json:"constant" example:"false"`
	HideInMenu bool   `json:"hideInMenu" example:"false"`
	KeepAlive  bool   `json:"keepAlive" example:"false"`
	MultiTab   bool   `json:"multiTab" example:"false"`
	Status     bool   `json:"status" example:"true"`
	ParentId   uint   `json:"parentId" example:"0"`
}

type SysMenuUpdateParams struct {
	MenuType   int    `json:"menuType" example:"0"`
	MenuName   string `json:"menuName" example:"用户管理"`
	RouteName  string `json:"routeName" example:"user"`
	RoutePath  string `json:"routePath" example:"/user"`
	Layout     string `json:"layout" example:"basic"`
	Component  string `json:"component" example:"UserList"`
	Icon       string `json:"icon" example:"user"`
	Order      int    `json:"order" example:"1"`
	Href       string `json:"href" example:"https://example.com"`
	Constant   bool   `json:"constant" example:"false"`
	HideInMenu bool   `json:"hideInMenu" example:"false"`
	KeepAlive  bool   `json:"keepAlive" example:"false"`
	MultiTab   bool   `json:"multiTab" example:"false"`
	Status     bool   `json:"status" example:"true"`
	ParentId   uint   `json:"parentId" example:"0"`
}

type SysMenuResponse struct {
	ID         uint   `json:"id"`
	MenuType   int    `json:"menuType"`
	MenuName   string `json:"menuName"`
	RouteName  string `json:"routeName"`
	RoutePath  string `json:"routePath"`
	Layout     string `json:"layout"`
	Component  string `json:"component"`
	Icon       string `json:"icon"`
	Order      int    `json:"order"`
	Href       string `json:"href"`
	Constant   bool   `json:"constant"`
	HideInMenu bool   `json:"hideInMenu"`
	KeepAlive  bool   `json:"keepAlive"`
	MultiTab   bool   `json:"multiTab"`
	Status     bool   `json:"status"`
	ParentId   uint   `json:"parentId"`
	CreatedAt  string `json:"createdAt"`
	UpdatedAt  string `json:"updatedAt"`
}
