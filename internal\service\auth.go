package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"time"

	"github.com/jinzhu/copier"
	"golang.org/x/crypto/bcrypt"
)

type AuthService interface {
	Login(ctx context.Context, req *v1.AuthLoginParams) (string, string, error)
	Userinfo(ctx context.Context, userId string) (*v1.AuthUserinfoResponse, error)
	UpdateUserinfo(ctx context.Context, userId string, req *v1.AuthUserinfoUpdateParams) error
	UpdatePassword(ctx context.Context, userId string, req *v1.AuthPasswordUpdateParams) error
	Refresh(ctx context.Context, refreshToken string) (string, string, error)
}

func NewAuthService(
	service *Service,
) AuthService {
	return &authService{
		Service: service,
	}
}

type authService struct {
	*Service
}

// Login 用户登录认证
func (s *authService) Login(ctx context.Context, req *v1.AuthLoginParams) (string, string, error) {
	// 根据用户名查询用户
	var user model.SysUser
	err := s.tm.Transaction(ctx, func(ctx context.Context) error {
		db := s.tm.(*repository.Repository).DB(ctx)
		return db.Where("username = ?", req.Username).First(&user).Error
	})
	if err != nil {
		return "", "", v1.ErrUnauthorized
	}

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password))
	if err != nil {
		return "", "", v1.ErrPasswordIncorrect
	}

	// 生成JWT令牌
	token, err := s.jwt.GenToken(user.UserId, time.Now().Add(time.Hour*24))
	if err != nil {
		return "", "", err
	}

	refreshToken, err := s.jwt.GenToken(user.UserId, time.Now().Add(time.Hour*24*7))
	if err != nil {
		return "", "", err
	}

	return token, refreshToken, nil
}

// Refresh 刷新令牌
func (s *authService) Refresh(ctx context.Context, refreshToken string) (string, string, error) {
	claims, err := s.jwt.ParseToken(refreshToken)
	if err != nil {
		return "", "", v1.ErrUnauthorized
	}
	userId := claims.UserId
	// 生成新的 token 和 refreshToken
	token, err := s.jwt.GenToken(userId, time.Now().Add(time.Hour*24))
	if err != nil {
		return "", "", err
	}
	newRefreshToken, err := s.jwt.GenToken(userId, time.Now().Add(time.Hour*24*7))
	if err != nil {
		return "", "", err
	}
	return token, newRefreshToken, nil
}

// Userinfo 获取用户信息
func (s *authService) Userinfo(ctx context.Context, userId string) (*v1.AuthUserinfoResponse, error) {
	// 查询用户
	var user model.SysUser
	err := s.tm.Transaction(ctx, func(ctx context.Context) error {
		db := s.tm.(*repository.Repository).DB(ctx)
		return db.Where("user_id = ?", userId).First(&user).Error
	})
	if err != nil {
		return nil, v1.ErrNotFound
	}

	// 创建响应结构体
	response := &v1.AuthUserinfoResponse{}
	if err := copier.Copy(&response.SysUserResponse, &user); err != nil {
		return nil, err
	}

	// 设置时间格式化字段
	response.SysUserResponse.CreatedAt = user.CreatedAt.Format(time.RFC3339)
	response.SysUserResponse.UpdatedAt = user.UpdatedAt.Format(time.RFC3339)

	// 获取部门信息
	if user.DeptId > 0 {
		var dept model.SysDept
		err := s.tm.Transaction(ctx, func(ctx context.Context) error {
			db := s.tm.(*repository.Repository).DB(ctx)
			return db.First(&dept, user.DeptId).Error
		})
		if err == nil {
			deptResponse := &v1.SysDeptResponse{}
			if err := copier.Copy(deptResponse, &dept); err == nil {
				deptResponse.CreatedAt = dept.CreatedAt.Format(time.RFC3339)
				deptResponse.UpdatedAt = dept.UpdatedAt.Format(time.RFC3339)
				response.Dept = deptResponse
			}
		}
	}

	// 获取角色信息
	if len(user.RoleIds) > 0 {
		var roles []*model.SysRole
		err := s.tm.Transaction(ctx, func(ctx context.Context) error {
			db := s.tm.(*repository.Repository).DB(ctx)
			// []int64切片 以避免gorm误解RoleIds为单个值而非数组
			return db.Where("id IN ?", []int64(user.RoleIds)).Find(&roles).Error
		})
		if err == nil {
			for _, role := range roles {
				roleResponse := &v1.SysRoleResponse{}
				if err := copier.Copy(roleResponse, role); err == nil {
					roleResponse.CreatedAt = role.CreatedAt.Format(time.RFC3339)
					roleResponse.UpdatedAt = role.UpdatedAt.Format(time.RFC3339)
					response.Roles = append(response.Roles, roleResponse)
				}
			}
		}
	}

	return response, nil
}

// UpdateUserinfo 更新用户信息
func (s *authService) UpdateUserinfo(ctx context.Context, userId string, req *v1.AuthUserinfoUpdateParams) error {
	// 查询用户
	var user model.SysUser
	err := s.tm.Transaction(ctx, func(ctx context.Context) error {
		db := s.tm.(*repository.Repository).DB(ctx)
		return db.Where("user_id = ?", userId).First(&user).Error
	})
	if err != nil {
		return v1.ErrNotFound
	}

	// 使用 copier 复制字段
	if err := copier.Copy(&user, req); err != nil {
		return err
	}

	// 更新用户信息
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		db := s.tm.(*repository.Repository).DB(ctx)
		return db.Save(&user).Error
	})
}

// UpdatePassword 更新用户密码
func (s *authService) UpdatePassword(ctx context.Context, userId string, req *v1.AuthPasswordUpdateParams) error {
	// 查询用户
	var user model.SysUser
	err := s.tm.Transaction(ctx, func(ctx context.Context) error {
		db := s.tm.(*repository.Repository).DB(ctx)
		return db.Where("user_id = ?", userId).First(&user).Error
	})
	if err != nil {
		return v1.ErrNotFound
	}

	// 验证两次密码是否一致
	if req.Password != req.Repass {
		return v1.ErrPasswordNotMatch
	}

	// 验证旧密码
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPass))
	if err != nil {
		return v1.ErrPasswordIncorrect
	}

	// 生成新密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	user.Password = string(hashedPassword)
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		db := s.tm.(*repository.Repository).DB(ctx)
		return db.Save(&user).Error
	})
}
