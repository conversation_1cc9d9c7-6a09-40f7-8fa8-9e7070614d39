package service

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/internal/repository"
	"daisy-server/pkg/pagination"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
)

type BusinessProductService interface {
	Create(ctx context.Context, req *v1.BusinessProductCreateParams) error
	Update(ctx context.Context, id uint, req *v1.BusinessProductUpdateParams) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*v1.BusinessProductResponse, error)
	List(ctx context.Context, params *pagination.Params) (*pagination.Result, error)
}

func NewBusinessProductService(
	service *Service,
	businessProductRepository repository.BusinessProductRepository,
	businessCompanyRepository repository.BusinessCompanyRepository,
) BusinessProductService {
	return &businessProductService{
		Service:                   service,
		businessProductRepository: businessProductRepository,
		businessCompanyRepository: businessCompanyRepository,
	}
}

type businessProductService struct {
	*Service
	businessProductRepository repository.BusinessProductRepository
	businessCompanyRepository repository.BusinessCompanyRepository
}

// 产品相关方法实现
func (s *businessProductService) Create(ctx context.Context, req *v1.BusinessProductCreateParams) error {
	product := &model.BusinessProduct{}
	if err := copier.Copy(product, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessProductRepository.Create(ctx, product); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessProductService) Update(ctx context.Context, id uint, req *v1.BusinessProductUpdateParams) error {
	product, err := s.businessProductRepository.Get(ctx, id)
	if err != nil {
		return err
	}

	if err := copier.Copy(product, req); err != nil {
		return err
	}

	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessProductRepository.Update(ctx, product); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessProductService) Delete(ctx context.Context, id uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessProductRepository.Delete(ctx, id); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessProductService) BatchDelete(ctx context.Context, ids []uint) error {
	return s.tm.Transaction(ctx, func(ctx context.Context) error {
		if err := s.businessProductRepository.BatchDelete(ctx, ids); err != nil {
			return err
		}
		return nil
	})
}

func (s *businessProductService) Get(ctx context.Context, id uint) (*v1.BusinessProductResponse, error) {
	product, err := s.businessProductRepository.Get(ctx, id)
	if err != nil {
		return nil, err
	}

	response := &v1.BusinessProductResponse{}
	if err := copier.Copy(response, product); err != nil {
		return nil, err
	}

	response.CreatedAt = product.CreatedAt.Format(time.RFC3339)
	response.UpdatedAt = product.UpdatedAt.Format(time.RFC3339)

	return response, nil
}

func (s *businessProductService) List(ctx context.Context, params *pagination.Params) (*pagination.Result, error) {
	// 状态筛选
	status := ctx.(*gin.Context).DefaultQuery("status", "")
	if status != "" {
		params.AddFilter("status", status)
	}

	// 名称筛选
	name := ctx.(*gin.Context).DefaultQuery("name", "")
	if name != "" {
		params.AddFilter("name_like", name)
	}

	// 编码筛选
	code := ctx.(*gin.Context).DefaultQuery("code", "")
	if code != "" {
		params.AddFilter("code_like", code)
	}

	// 公司筛选
	companyId := ctx.(*gin.Context).DefaultQuery("companyId", "")
	if companyId != "" {
		params.AddFilter("company_id", companyId)
	}

	// 类型筛选
	productType := ctx.(*gin.Context).DefaultQuery("type", "")
	if productType != "" {
		params.AddFilter("type", productType)
	}

	// 标志筛选
	flag := ctx.(*gin.Context).DefaultQuery("flag", "")
	if flag != "" {
		params.AddFilter("flag_any", flag)
	}

	products, total, err := s.businessProductRepository.List(ctx, params)
	if err != nil {
		return nil, err
	}

	// 检查是否需要展开公司信息
	expandCompany := ctx.(*gin.Context).Query("_expand") == "company"

	// 将原始记录转换为API响应格式
	records := make([]*v1.BusinessProductResponse, 0, len(products))
	for _, product := range products {
		productResponse := &v1.BusinessProductResponse{}
		if err := copier.Copy(productResponse, product); err != nil {
			return nil, err
		}

		productResponse.CreatedAt = product.CreatedAt.Format(time.RFC3339)
		productResponse.UpdatedAt = product.UpdatedAt.Format(time.RFC3339)

		// 如果需要展开公司信息，则获取公司详情
		if expandCompany && product.CompanyId > 0 {
			company, err := s.businessCompanyRepository.Get(ctx, product.CompanyId)
			if err == nil {
				companyResponse := &v1.BusinessCompanyResponse{}
				if err := copier.Copy(companyResponse, company); err == nil {
					companyResponse.CreatedAt = company.CreatedAt.Format(time.RFC3339)
					companyResponse.UpdatedAt = company.UpdatedAt.Format(time.RFC3339)
					productResponse.Company = companyResponse
				}
			}
		}

		records = append(records, productResponse)
	}

	return &pagination.Result{
		Records: records,
		Total:   total,
	}, nil
}
