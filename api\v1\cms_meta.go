package v1

type CmsMetaCreateParams struct {
	Name     string `json:"name" binding:"required" example:"技术文章"`
	Slug     string `json:"slug" binding:"required" example:"tech"`
	Cover    string `json:"cover" example:"https://example.com/cover.jpg"`
	Summary  string `json:"summary" example:"技术相关文章"`
	Order    int    `json:"order" example:"1"`
	Status   bool   `json:"status" example:"true"`
	ParentId uint   `json:"parentId" example:"0"`
}

type CmsMetaUpdateParams struct {
	Name     string `json:"name" example:"技术文章"`
	Slug     string `json:"slug" example:"tech"`
	Cover    string `json:"cover" example:"https://example.com/cover.jpg"`
	Summary  string `json:"summary" example:"技术相关文章"`
	Order    int    `json:"order" example:"1"`
	Status   bool   `json:"status" example:"true"`
	ParentId uint   `json:"parentId" example:"0"`
}

type CmsMetaResponse struct {
	ID        uint   `json:"id"`
	Name      string `json:"name"`
	Slug      string `json:"slug"`
	Cover     string `json:"cover"`
	Summary   string `json:"summary"`
	Order     int    `json:"order"`
	Status    bool   `json:"status"`
	ParentId  uint   `json:"parentId"`
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}
