package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type BusinessAppRepository interface {
	Create(ctx context.Context, app *model.BusinessApp) error
	Update(ctx context.Context, app *model.BusinessApp) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.BusinessApp, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.BusinessApp, int64, error)
}

func NewBusinessAppRepository(
	repository *Repository,
) BusinessAppRepository {
	return &businessAppRepository{
		Repository: repository,
	}
}

type businessAppRepository struct {
	*Repository
}

func (r *businessAppRepository) Create(ctx context.Context, app *model.BusinessApp) error {
	if err := r.DB(ctx).Create(app).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessAppRepository) Update(ctx context.Context, app *model.BusinessApp) error {
	if err := r.DB(ctx).Save(app).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessAppRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessApp{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessAppRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessApp{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessAppRepository) Get(ctx context.Context, id uint) (*model.BusinessApp, error) {
	var app model.BusinessApp
	if err := r.DB(ctx).First(&app, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &app, nil
}

func (r *businessAppRepository) List(ctx context.Context, params *pagination.Params) ([]*model.BusinessApp, int64, error) {
	var records []*model.BusinessApp
	var total int64

	db := r.DB(ctx).Model(&model.BusinessApp{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.BusinessApp{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
