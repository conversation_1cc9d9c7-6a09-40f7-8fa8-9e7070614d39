package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/casbin/casbin/v2"
	casbinmodel "github.com/casbin/casbin/v2/model"
	"github.com/casbin/casbin/v2/persist"
	rediswatcher "github.com/casbin/redis-watcher/v2"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"go.uber.org/zap"

	v1 "daisy-server/api/v1"
	"daisy-server/internal/repository"
	"daisy-server/pkg/log"
	"daisy-server/pkg/rbac"
)

// 配置路径常量
const (
	rbacModelPath     = "security.rbac.model"
	rbacPolicyPath    = "security.rbac.policy"
	redisAddrPath     = "data.redis.addr"
	redisPasswordPath = "data.redis.password"
	redisDBPath       = "data.redis.db"
	watcherChannel    = "/casbin"
)

// 模型配置路径常量
const (
	requestDefPath   = "security.rbac.model.request_definition.r"
	policyDefPath    = "security.rbac.model.policy_definition.p"
	roleDefPath      = "security.rbac.model.role_definition.g"
	policyEffectPath = "security.rbac.model.policy_effect.e"
	matchersPath     = "security.rbac.model.matchers.m"
)

// RBAC 结构体，用于实现RBAC中间件
type RBAC struct {
	enforcer   *casbin.Enforcer
	repository *repository.Repository
	logger     *log.Logger
	watcher    persist.Watcher
}

// modelConfig 模型配置结构
type modelConfig struct {
	requestDef   []string
	policyDef    []string
	roleDef      []string
	policyEffect string
	matchers     string
}

// loadModelConfig 从配置中加载模型配置
func loadModelConfig(conf *viper.Viper) (*modelConfig, error) {
	config := &modelConfig{}

	// 检查配置是否存在
	if !conf.IsSet(rbacModelPath) {
		return nil, fmt.Errorf("未找到RBAC模型配置")
	}

	// 加载请求定义
	if conf.IsSet(requestDefPath) {
		config.requestDef = conf.GetStringSlice(requestDefPath)
	} else {
		return nil, fmt.Errorf("RBAC模型配置缺少request_definition")
	}

	// 加载策略定义
	if conf.IsSet(policyDefPath) {
		config.policyDef = conf.GetStringSlice(policyDefPath)
	} else {
		return nil, fmt.Errorf("RBAC模型配置缺少policy_definition")
	}

	// 加载角色定义
	if conf.IsSet(roleDefPath) {
		config.roleDef = conf.GetStringSlice(roleDefPath)
	} else {
		return nil, fmt.Errorf("RBAC模型配置缺少role_definition")
	}

	// 加载策略效果
	if conf.IsSet(policyEffectPath) {
		config.policyEffect = conf.GetString(policyEffectPath)
	} else {
		return nil, fmt.Errorf("RBAC模型配置缺少policy_effect")
	}

	// 加载匹配器
	if conf.IsSet(matchersPath) {
		config.matchers = conf.GetString(matchersPath)
	} else {
		return nil, fmt.Errorf("RBAC模型配置缺少matchers")
	}

	return config, nil
}

// buildCasbinModel 构建Casbin模型
func buildCasbinModel(config *modelConfig) casbinmodel.Model {
	m := casbinmodel.NewModel()

	m.AddDef("r", "r", strings.Join(config.requestDef, ", "))
	m.AddDef("p", "p", strings.Join(config.policyDef, ", "))
	m.AddDef("g", "g", strings.Join(config.roleDef, ", "))
	m.AddDef("e", "e", config.policyEffect)
	m.AddDef("m", "m", config.matchers)

	return m
}

// loadPolicies 从配置中加载策略
func loadPolicies(enforcer *casbin.Enforcer, conf *viper.Viper) error {
	if !conf.IsSet(rbacPolicyPath) {
		return nil // 策略是可选的
	}

	policies := conf.Get(rbacPolicyPath)
	if policies == nil {
		return nil
	}

	policyList, ok := policies.([]interface{})
	if !ok {
		return fmt.Errorf("RBAC策略配置格式错误")
	}

	for _, p := range policyList {
		policy, ok := p.([]interface{})
		if !ok {
			continue
		}

		// 将策略转换为字符串切片
		strPolicy := make([]string, 0, len(policy))
		for _, item := range policy {
			strItem, ok := item.(string)
			if !ok {
				strItem = fmt.Sprintf("%v", item)
			}
			strPolicy = append(strPolicy, strItem)
		}

		// 添加策略（至少需要2个元素）
		if len(strPolicy) >= 2 {
			if _, err := enforcer.AddPolicy(strPolicy); err != nil {
				return fmt.Errorf("添加策略失败: %w", err)
			}
		}
	}

	return nil
}

// setupWatcher 设置Redis Watcher
func setupWatcher(enforcer *casbin.Enforcer, conf *viper.Viper, logger *log.Logger) (persist.Watcher, error) {
	watcher, err := rediswatcher.NewWatcher(
		conf.GetString(redisAddrPath),
		rediswatcher.WatcherOptions{
			Options: redis.Options{
				Password: conf.GetString(redisPasswordPath),
				DB:       conf.GetInt(redisDBPath),
			},
			Channel: watcherChannel,
		},
	)
	if err != nil {
		return nil, fmt.Errorf("初始化Redis Watcher失败: %w", err)
	}

	// 设置回调函数，当策略变更时更新enforcer
	err = watcher.SetUpdateCallback(func(s string) {
		logger.Info("Casbin策略已更新", zap.String("data", s))
		if err := enforcer.LoadPolicy(); err != nil {
			logger.Error("重新加载策略失败", zap.Error(err))
		}
	})
	if err != nil {
		watcher.Close()
		return nil, fmt.Errorf("设置Watcher回调函数失败: %w", err)
	}

	// 将watcher设置到enforcer
	if err = enforcer.SetWatcher(watcher); err != nil {
		watcher.Close()
		return nil, fmt.Errorf("设置Watcher到Enforcer失败: %w", err)
	}

	return watcher, nil
}

// NewRBAC 创建RBAC中间件实例
func NewRBAC(conf *viper.Viper, repository *repository.Repository, logger *log.Logger, redisClient *redis.Client) (*RBAC, error) {
	// 加载模型配置
	modelConfig, err := loadModelConfig(conf)
	if err != nil {
		return nil, err
	}

	// 构建Casbin模型
	m := buildCasbinModel(modelConfig)

	// 初始化Enforcer
	enforcer, err := casbin.NewEnforcer(m)
	if err != nil {
		return nil, fmt.Errorf("初始化Casbin Enforcer失败: %w", err)
	}

	// 加载策略
	if err := loadPolicies(enforcer, conf); err != nil {
		return nil, err
	}

	// 设置Watcher
	watcher, err := setupWatcher(enforcer, conf, logger)
	if err != nil {
		return nil, err
	}

	return &RBAC{
		enforcer:   enforcer,
		repository: repository,
		logger:     logger,
		watcher:    watcher,
	}, nil
}

// RBACAuth 基于RBAC的权限控制中间件
func RBACAuth(r *rbac.RBAC, logger *log.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userId := rbac.GetUserId(c)
		if userId == "" {
			logger.WithContext(c).Warn("用户未认证")
			v1.HandleError(c, http.StatusUnauthorized, v1.ErrUnauthorized, nil)
			c.Abort()
			return
		}

		// 获取请求路径和方法
		reqPath := c.Request.URL.Path
		reqMethod := c.Request.Method

		// 检查用户是否有权限访问
		hasPermission, err := r.CheckPermission(context.Background(), userId, reqPath, reqMethod)
		if err != nil {
			logger.WithContext(c).Error("检查权限失败", zap.Error(err))
			v1.HandleError(c, http.StatusInternalServerError, v1.ErrInternalServerError, nil)
			c.Abort()
			return
		}

		if hasPermission {
			logger.WithContext(c).Debug("用户访问已授权",
				zap.String("userId", userId),
				zap.String("path", reqPath),
				zap.String("method", reqMethod))
			c.Next()
		} else {
			logger.WithContext(c).Warn("用户访问未授权",
				zap.String("userId", userId),
				zap.String("path", reqPath),
				zap.String("method", reqMethod))
			v1.HandleError(c, http.StatusForbidden, v1.ErrForbidden, nil)
			c.Abort()
		}
	}
}

// Close 关闭Watcher连接
func (r *RBAC) Close() error {
	if r.watcher != nil {
		r.watcher.Close()
	}
	return nil
}
