package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BusinessAppHandler struct {
	*Handler
	businessAppService service.BusinessAppService
}

func NewBusinessAppHandler(
	handler *Handler,
	businessAppService service.BusinessAppService,
) *BusinessAppHandler {
	return &BusinessAppHandler{
		Handler:            handler,
		businessAppService: businessAppService,
	}
}

// Create godoc
// @Summary 创建应用
// @Schemes
// @Description 创建新的应用记录
// @Tags 业务模块,应用管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BusinessAppCreateParams true "应用信息"
// @Success 200 {object} v1.Response
// @Router /business/apps [post]
func (h *BusinessAppHandler) Create(ctx *gin.Context) {
	var req v1.BusinessAppCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessAppService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新应用
// @Schemes
// @Description 更新指定ID的应用信息
// @Tags 业务模块,应用管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "应用ID"
// @Param request body v1.BusinessAppUpdateParams true "应用信息"
// @Success 200 {object} v1.Response
// @Router /business/apps/{id} [patch]
func (h *BusinessAppHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.BusinessAppUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessAppService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除应用
// @Schemes
// @Description 删除指定ID的应用
// @Tags 业务模块,应用管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "应用ID"
// @Success 200 {object} v1.Response
// @Router /business/apps/{id} [delete]
func (h *BusinessAppHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessAppService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除应用
// @Schemes
// @Description 批量删除指定IDs的应用
// @Tags 业务模块,应用管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "应用IDs"
// @Success 200 {object} v1.Response
// @Router /business/apps [delete]
func (h *BusinessAppHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessAppService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取应用
// @Schemes
// @Description 获取指定ID的应用信息
// @Tags 业务模块,应用管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "应用ID"
// @Success 200 {object} v1.Response{data=v1.BusinessAppResponse}
// @Router /business/apps/{id} [get]
func (h *BusinessAppHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	app, err := h.businessAppService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, app)
}

// List godoc
// @Summary 获取应用列表
// @Schemes
// @Description 分页获取应用列表
// @Tags 业务模块,应用管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param name query string false "名称筛选" example:"应用名称"
// @Param slug query string false "别名筛选" example:"app-slug"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.BusinessAppResponse}}
// @Router /business/apps [get]
func (h *BusinessAppHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.businessAppService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
