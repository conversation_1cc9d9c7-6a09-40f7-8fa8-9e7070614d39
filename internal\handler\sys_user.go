package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SysUserHandler struct {
	*Handler
	sysUserService service.SysUserService
}

func NewSysUserHandler(
	handler *Handler,
	sysUserService service.SysUserService,
) *SysUserHandler {
	return &SysUserHandler{
		Handler:        handler,
		sysUserService: sysUserService,
	}
}

// Create godoc
// @Summary 创建用户
// @Schemes
// @Description 创建新的用户
// @Tags 系统模块,用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.SysUserCreateParams true "用户信息"
// @Success 200 {object} v1.Response
// @Router /system/users [post]
func (h *SysUserHandler) Create(ctx *gin.Context) {
	var req v1.SysUserCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysUserService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新用户
// @Schemes
// @Description 更新指定ID的用户信息
// @Tags 系统模块,用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "用户ID"
// @Param request body v1.SysUserUpdateParams true "用户信息"
// @Success 200 {object} v1.Response
// @Router /system/users/{id} [patch]
func (h *SysUserHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.SysUserUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysUserService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除用户
// @Schemes
// @Description 删除指定ID的用户
// @Tags 系统模块,用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "用户ID"
// @Success 200 {object} v1.Response
// @Router /system/users/{id} [delete]
func (h *SysUserHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysUserService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除用户
// @Schemes
// @Description 批量删除指定IDs的用户
// @Tags 系统模块,用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "用户IDs"
// @Success 200 {object} v1.Response
// @Router /system/users [delete]
func (h *SysUserHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysUserService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取用户
// @Schemes
// @Description 获取指定ID的用户信息
// @Tags 系统模块,用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "用户ID"
// @Success 200 {object} v1.Response{data=v1.SysUserResponse}
// @Router /system/users/{id} [get]
func (h *SysUserHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	user, err := h.sysUserService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, user)
}

// List godoc
// @Summary 获取用户列表
// @Schemes
// @Description 分页获取用户列表
// @Tags 系统模块,用户管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysUserResponse}}
// @Router /system/users [get]
func (h *SysUserHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.sysUserService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
