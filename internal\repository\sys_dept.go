package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type SysDeptRepository interface {
	Create(ctx context.Context, dept *model.SysDept) error
	Update(ctx context.Context, dept *model.SysDept) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.SysDept, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.SysDept, int64, error)
}

func NewSysDeptRepository(
	repository *Repository,
) SysDeptRepository {
	return &sysDeptRepository{
		Repository: repository,
	}
}

type sysDeptRepository struct {
	*Repository
}

func (r *sysDeptRepository) Create(ctx context.Context, dept *model.SysDept) error {
	if err := r.DB(ctx).Create(dept).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysDeptRepository) Update(ctx context.Context, dept *model.SysDept) error {
	if err := r.DB(ctx).Save(dept).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysDeptRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.SysDept{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysDeptRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.SysDept{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *sysDeptRepository) Get(ctx context.Context, id uint) (*model.SysDept, error) {
	var dept model.SysDept
	if err := r.DB(ctx).First(&dept, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &dept, nil
}

func (r *sysDeptRepository) List(ctx context.Context, params *pagination.Params) ([]*model.SysDept, int64, error) {
	var records []*model.SysDept
	var total int64

	db := r.DB(ctx).Model(&model.SysDept{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.SysDept{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
