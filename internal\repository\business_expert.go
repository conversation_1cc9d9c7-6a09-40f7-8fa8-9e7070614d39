package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type BusinessExpertRepository interface {
	Create(ctx context.Context, expert *model.BusinessExpert) error
	Update(ctx context.Context, expert *model.BusinessExpert) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.BusinessExpert, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.BusinessExpert, int64, error)
}

func NewBusinessExpertRepository(
	repository *Repository,
) BusinessExpertRepository {
	return &businessExpertRepository{
		Repository: repository,
	}
}

type businessExpertRepository struct {
	*Repository
}

func (r *businessExpertRepository) Create(ctx context.Context, expert *model.BusinessExpert) error {
	if err := r.DB(ctx).Create(expert).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessExpertRepository) Update(ctx context.Context, expert *model.BusinessExpert) error {
	if err := r.DB(ctx).Save(expert).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessExpertRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessExpert{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessExpertRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessExpert{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessExpertRepository) Get(ctx context.Context, id uint) (*model.BusinessExpert, error) {
	var expert model.BusinessExpert
	if err := r.DB(ctx).First(&expert, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &expert, nil
}

func (r *businessExpertRepository) List(ctx context.Context, params *pagination.Params) ([]*model.BusinessExpert, int64, error) {
	var records []*model.BusinessExpert
	var total int64

	db := r.DB(ctx).Model(&model.BusinessExpert{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.BusinessExpert{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
