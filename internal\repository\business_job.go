package repository

import (
	"context"
	v1 "daisy-server/api/v1"
	"daisy-server/internal/model"
	"daisy-server/pkg/pagination"
	"errors"

	"gorm.io/gorm"
)

type BusinessJobRepository interface {
	Create(ctx context.Context, job *model.BusinessJob) error
	Update(ctx context.Context, job *model.BusinessJob) error
	Delete(ctx context.Context, id uint) error
	BatchDelete(ctx context.Context, ids []uint) error
	Get(ctx context.Context, id uint) (*model.BusinessJob, error)
	List(ctx context.Context, params *pagination.Params) ([]*model.BusinessJob, int64, error)
}

func NewBusinessJobRepository(
	repository *Repository,
) BusinessJobRepository {
	return &businessJobRepository{
		Repository: repository,
	}
}

type businessJobRepository struct {
	*Repository
}

func (r *businessJobRepository) Create(ctx context.Context, job *model.BusinessJob) error {
	if err := r.DB(ctx).Create(job).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessJobRepository) Update(ctx context.Context, job *model.BusinessJob) error {
	if err := r.DB(ctx).Save(job).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessJobRepository) Delete(ctx context.Context, id uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessJob{}, id).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessJobRepository) BatchDelete(ctx context.Context, ids []uint) error {
	if err := r.DB(ctx).Delete(&model.BusinessJob{}, ids).Error; err != nil {
		return err
	}
	return nil
}

func (r *businessJobRepository) Get(ctx context.Context, id uint) (*model.BusinessJob, error) {
	var job model.BusinessJob
	if err := r.DB(ctx).First(&job, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, v1.ErrNotFound
		}
		return nil, err
	}
	return &job, nil
}

func (r *businessJobRepository) List(ctx context.Context, params *pagination.Params) ([]*model.BusinessJob, int64, error) {
	var records []*model.BusinessJob
	var total int64

	db := r.DB(ctx).Model(&model.BusinessJob{})

	// 使用GetResult方法直接获取分页结果
	total, err := params.GetResult(db, &records, &model.BusinessJob{})
	if err != nil {
		return nil, 0, err
	}

	return records, total, nil
}
