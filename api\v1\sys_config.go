package v1

type SysConfigParam struct {
	ID      string `json:"id"`
	Code    string `json:"code"`
	Name    string `json:"name"`
	Value   string `json:"value"`
	Summary string `json:"summary"`
	Status  bool   `json:"status"`
}

type SysConfigCreateParams struct {
	Name    string           `json:"name" binding:"required" example:"系统配置"`
	Code    string           `json:"code" binding:"required" example:"system"`
	Summary string           `json:"summary" example:"系统基础配置"`
	Status  bool             `json:"status" example:"true"`
	Params  []SysConfigParam `json:"params"`
}

type SysConfigUpdateParams struct {
	Name    string           `json:"name" example:"系统配置"`
	Code    string           `json:"code" example:"system"`
	Summary string           `json:"summary" example:"系统基础配置"`
	Status  bool             `json:"status" example:"true"`
	Params  []SysConfigParam `json:"params"`
}

type SysConfigResponse struct {
	ID        uint             `json:"id"`
	Name      string           `json:"name"`
	Code      string           `json:"code"`
	Summary   string           `json:"summary"`
	Status    bool             `json:"status"`
	Params    []SysConfigParam `json:"params"`
	CreatedAt string           `json:"createdAt"`
	UpdatedAt string           `json:"updatedAt"`
}
