package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SysDeptHandler struct {
	*Handler
	sysDeptService service.SysDeptService
}

func NewSysDeptHandler(
	handler *Handler,
	sysDeptService service.SysDeptService,
) *SysDeptHandler {
	return &SysDeptHandler{
		Handler:        handler,
		sysDeptService: sysDeptService,
	}
}

// Create godoc
// @Summary 创建部门
// @Schemes
// @Description 创建新的部门
// @Tags 系统模块,部门管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.SysDeptCreateParams true "部门信息"
// @Success 200 {object} v1.Response
// @Router /system/depts [post]
func (h *SysDeptHandler) Create(ctx *gin.Context) {
	var req v1.SysDeptCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysDeptService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新部门
// @Schemes
// @Description 更新指定ID的部门信息
// @Tags 系统模块,部门管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "部门ID"
// @Param request body v1.SysDeptUpdateParams true "部门信息"
// @Success 200 {object} v1.Response
// @Router /system/depts/{id} [patch]
func (h *SysDeptHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.SysDeptUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysDeptService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除部门
// @Schemes
// @Description 删除指定ID的部门
// @Tags 系统模块,部门管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "部门ID"
// @Success 200 {object} v1.Response
// @Router /system/depts/{id} [delete]
func (h *SysDeptHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysDeptService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除部门
// @Schemes
// @Description 批量删除指定IDs的部门
// @Tags 系统模块,部门管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "部门IDs"
// @Success 200 {object} v1.Response
// @Router /system/depts [delete]
func (h *SysDeptHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysDeptService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取部门
// @Schemes
// @Description 获取指定ID的部门信息
// @Tags 系统模块,部门管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "部门ID"
// @Success 200 {object} v1.Response{data=v1.SysDeptResponse}
// @Router /system/depts/{id} [get]
func (h *SysDeptHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	dept, err := h.sysDeptService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, dept)
}

// List godoc
// @Summary 获取部门列表
// @Schemes
// @Description 分页获取部门列表
// @Tags 系统模块,部门管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysDeptResponse}}
// @Router /system/depts [get]
func (h *SysDeptHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.sysDeptService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
