package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type BusinessProductHandler struct {
	*Handler
	businessProductService service.BusinessProductService
}

func NewBusinessProductHandler(
	handler *Handler,
	businessProductService service.BusinessProductService,
) *BusinessProductHandler {
	return &BusinessProductHandler{
		Handler:                handler,
		businessProductService: businessProductService,
	}
}

// Create godoc
// @Summary 创建产品
// @Schemes
// @Description 创建新的产品记录
// @Tags 业务模块,产品管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BusinessProductCreateParams true "产品信息"
// @Success 200 {object} v1.Response
// @Router /business/products [post]
func (h *BusinessProductHandler) Create(ctx *gin.Context) {
	var req v1.BusinessProductCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessProductService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新产品
// @Schemes
// @Description 更新指定ID的产品信息
// @Tags 业务模块,产品管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "产品ID"
// @Param request body v1.BusinessProductUpdateParams true "产品信息"
// @Success 200 {object} v1.Response
// @Router /business/products/{id} [patch]
func (h *BusinessProductHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.BusinessProductUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessProductService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除产品
// @Schemes
// @Description 删除指定ID的产品
// @Tags 业务模块,产品管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "产品ID"
// @Success 200 {object} v1.Response
// @Router /business/products/{id} [delete]
func (h *BusinessProductHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessProductService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除产品
// @Schemes
// @Description 批量删除指定IDs的产品
// @Tags 业务模块,产品管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "产品IDs"
// @Success 200 {object} v1.Response
// @Router /business/products [delete]
func (h *BusinessProductHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.businessProductService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取产品
// @Schemes
// @Description 获取指定ID的产品信息
// @Tags 业务模块,产品管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "产品ID"
// @Success 200 {object} v1.Response{data=v1.BusinessProductResponse}
// @Router /business/products/{id} [get]
func (h *BusinessProductHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	product, err := h.businessProductService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, product)
}

// List godoc
// @Summary 获取产品列表
// @Schemes
// @Description 分页获取产品列表
// @Tags 业务模块,产品管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Param company_id query int false "公司ID筛选" example:"1"
// @Param name query string false "产品名称筛选" example:"产品名"
// @Param code query string false "产品编码筛选" example:"PRD-001"
// @Param type query int false "产品类型筛选" example:"1"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.BusinessProductResponse}}
// @Router /business/products [get]
func (h *BusinessProductHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.businessProductService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
