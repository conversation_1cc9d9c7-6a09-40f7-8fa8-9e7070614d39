package handler

import (
	v1 "daisy-server/api/v1"
	"daisy-server/internal/service"
	"daisy-server/pkg/pagination"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type SysRoleHandler struct {
	*Handler
	sysRoleService service.SysRoleService
}

func NewSysRoleHandler(
	handler *Handler,
	sysRoleService service.SysRoleService,
) *SysRoleHandler {
	return &SysRoleHandler{
		Handler:        handler,
		sysRoleService: sysRoleService,
	}
}

// Create godoc
// @Summary 创建角色
// @Schemes
// @Description 创建新的角色
// @Tags 系统模块,角色管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.SysRoleCreateParams true "角色信息"
// @Success 200 {object} v1.Response
// @Router /system/roles [post]
func (h *SysRoleHandler) Create(ctx *gin.Context) {
	var req v1.SysRoleCreateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysRoleService.Create(ctx, &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Update godoc
// @Summary 更新角色
// @Schemes
// @Description 更新指定ID的角色信息
// @Tags 系统模块,角色管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "角色ID"
// @Param request body v1.SysRoleUpdateParams true "角色信息"
// @Success 200 {object} v1.Response
// @Router /system/roles/{id} [patch]
func (h *SysRoleHandler) Update(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	var req v1.SysRoleUpdateParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysRoleService.Update(ctx, uint(id), &req); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Delete godoc
// @Summary 删除角色
// @Schemes
// @Description 删除指定ID的角色
// @Tags 系统模块,角色管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "角色ID"
// @Success 200 {object} v1.Response
// @Router /system/roles/{id} [delete]
func (h *SysRoleHandler) Delete(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysRoleService.Delete(ctx, uint(id)); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// BatchDelete godoc
// @Summary 批量删除角色
// @Schemes
// @Description 批量删除指定IDs的角色
// @Tags 系统模块,角色管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body v1.BatchDeleteParams true "角色IDs"
// @Success 200 {object} v1.Response
// @Router /system/roles [delete]
func (h *SysRoleHandler) BatchDelete(ctx *gin.Context) {
	var req v1.BatchDeleteParams
	if err := ctx.ShouldBindJSON(&req); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	if err := h.sysRoleService.BatchDelete(ctx, req.IDs); err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, nil)
}

// Get godoc
// @Summary 获取角色
// @Schemes
// @Description 获取指定ID的角色信息
// @Tags 系统模块,角色管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param id path int true "角色ID"
// @Success 200 {object} v1.Response{data=v1.SysRoleResponse}
// @Router /system/roles/{id} [get]
func (h *SysRoleHandler) Get(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	role, err := h.sysRoleService.Get(ctx, uint(id))
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, role)
}

// List godoc
// @Summary 获取角色列表
// @Schemes
// @Description 分页获取角色列表
// @Tags 系统模块,角色管理
// @Accept json
// @Produce json
// @Security Bearer
// @Param _page query int false "当前页码(默认值: 1)" example:"1"
// @Param _limit query int false "每页数量(默认值: 10)" example:"10"
// @Param _sort query string false "排序字段，多个字段用逗号分隔" example:"id"
// @Param _order query string false "排序方式，asc或desc，多个方式用逗号分隔" example:"desc"
// @Param q query string false "全文搜索" example:"关键词"
// @Param status query bool false "状态筛选" example:"true"
// @Success 200 {object} v1.Response{data=pagination.Result{records=[]v1.SysRoleResponse}}
// @Router /system/roles [get]
func (h *SysRoleHandler) List(ctx *gin.Context) {
	var params pagination.Params
	if err := ctx.ShouldBindQuery(&params); err != nil {
		v1.HandleError(ctx, http.StatusBadRequest, v1.ErrBadRequest, nil)
		return
	}

	result, err := h.sysRoleService.List(ctx, &params)
	if err != nil {
		v1.HandleError(ctx, http.StatusInternalServerError, err, nil)
		return
	}

	v1.HandleSuccess(ctx, result)
}
